# ERP业务SearchSelect开发规范

## 概述

本规范基于ax-admin系统中品牌搜索选择器(BrandSearch)的最佳实践，定义了ERP业务SearchSelect组件的标准架构、开发模式和代码规范。

SearchSelect是一个复合组件，结合了下拉选择和弹窗表单功能，用于在表单中选择业务实体（如品牌、供应商、客户等）。

## 目录结构规范

### 标准目录结构
```
{ModuleName}Search/
├── {ModuleName}Search.vue          # 主组件（必需）
├── component/                      # 子组件目录（必需）
│   └── {ModuleName}SearchModal.vue # 弹窗表单组件（必需）
├── config/                         # 配置目录（必需）
│   └── rule.ts                     # 表单验证规则（必需）
├── index.ts                        # 导出文件（必需）
└── service/                        # 服务目录（必需）
    └── {ModuleName}SearchService.ts # 业务服务（必需）
```

### 命名约定
- **文件名**：使用PascalCase，如 `BrandSearch.vue`
- **组件名**：与文件名保持一致
- **服务类**：以SearchService后缀，如 `BrandSearchService`
- **配置文件**：小写，如 `rule.ts`

## 架构设计模式

### 1. 分层架构
```
┌─────────────────────────────┐
│     视图层 (View)           │  主组件 + 弹窗组件
├─────────────────────────────┤
│    服务层 (Service)         │  继承ListCrudService
├─────────────────────────────┤
│    配置层 (Config)          │  表单验证规则
├─────────────────────────────┤
│     API层 (API)             │  业务API调用
└─────────────────────────────┘
```

### 2. 依赖注入模式
```typescript
// 服务提供者（主组件）
const service = inject<BrandSearchService>(BRAND_SEARCH_KEY, new BrandSearchService())!
provide(BRAND_SEARCH_KEY, service)

// 服务消费者（弹窗组件）
const brandService = inject<BrandSearchService>(BRAND_SEARCH_KEY)!
```

### 3. 组件导入规则
- ✅ **自动导入**：基础组件（SearchSelect、Dialog、Form等）
- ✅ **手动导入**：同级目录下的组件和本地.ts文件
- ❌ **禁止导入**：PrimeVue原生组件

## 文件开发规范

### 1. 主组件 (`{ModuleName}Search.vue`)

#### 基本结构
```vue
<script setup lang="ts">
import BrandSearchModal from './component/BrandSearchModal.vue'
import { BRAND_SEARCH_KEY, BrandSearchService } from './service/BrandSearchService'

const { allowAdd = false, allowEdit = false, allowDelete = false } = defineProps<{
  allowAdd?: boolean
  allowEdit?: boolean
  allowDelete?: boolean
}>()

// 品牌服务
const service = inject<BrandSearchService>(BRAND_SEARCH_KEY, new BrandSearchService())!

provide(BRAND_SEARCH_KEY, service)
</script>

<template>
  <SearchSelect
    v-model="service.selectedKey"
    :options="service.listData || []"
    :option-label="service.labelKey"
    :option-value="service.valueKey"
    :placeholder="service.placeholder"
    :on-add="allowAdd ? () => service.openAddForm?.() : undefined"
    :on-edit="allowEdit ? (value: number) => service.openEditForm?.(value) : undefined"
    :on-delete="allowDelete ? (value: number) => service.deleteEntity?.(value) : undefined"
    v-bind="$attrs"
  />
  <BrandSearchModal />
</template>
```

#### 关键规范
- **Props设计**：提供`allowAdd`、`allowEdit`、`allowDelete`三个可选操作权限
- **服务注入**：使用inject获取服务实例，如不存在则创建新实例
- **双向绑定**：v-model绑定到`service.selectedKey`
- **权限控制**：根据props条件性传递操作回调函数
- **属性透传**：使用`v-bind="$attrs"`透传其他属性

### 2. 弹窗组件 (`{ModuleName}SearchModal.vue`)

#### 基本结构
```vue
<script setup lang="ts">
import { rules } from '../config/rule'
import { BRAND_SEARCH_KEY, type BrandSearchService } from '../service/BrandSearchService'

// 获取服务实例
const brandService = inject<BrandSearchService>(BRAND_SEARCH_KEY)!
// 当前激活的标签页
const activeTab = ref('basic')
</script>

<template>
  <Dialog
    v-model:visible="brandService.formOpen" :header="brandService.formTitle"
  >
    <Form
      :ref="brandService.formRef" :form-data="brandService.formData" :rules="rules"
    >
      <!-- 标签页导航 -->
      <BaseTabs v-model:value="activeTab">
        <BaseTabList>
          <BaseTab value="basic">
            📋 基本信息
          </BaseTab>
          <BaseTab value="brand">
            🏷️ 品牌信息
          </BaseTab>
        </BaseTabList>

        <BaseTabPanels>
          <!-- 基本信息标签页 -->
          <BaseTabsPanel value="basic">
            <Grid :cols="2" :responsive="false">
              <GridCol>
                <FormItem label="品牌编码 (自动生成)" name="brandCode">
                  <InputText disabled />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="品牌名称" name="brandName">
                  <InputText placeholder="请输入品牌名称" />
                </FormItem>
              </GridCol>
              <!-- 更多表单项 -->
            </Grid>
          </BaseTabsPanel>
          <!-- 更多标签页 -->
        </BaseTabPanels>
      </BaseTabs>
    </Form>

    <template #footer>
      <Button label="取消" severity="danger" @click="brandService.closeForm()" />
      <Button label="保存" @click="brandService.submitFormAndRefresh()" />
    </template>
  </Dialog>
</template>
```

#### 关键规范
- **服务注入**：从主组件注入的服务实例中获取状态和方法
- **表单结构**：使用标签页组织表单字段，分为基本信息和扩展信息
- **验证规则**：引入配置文件中的规则，只对必填字段设置验证
- **布局方式**：使用Grid布局，默认2列，`:responsive="false"`
- **自动生成字段**：标签添加"(自动生成)"，输入框设置为disabled

### 3. 验证规则 (`config/rule.ts`)

#### 基本结构
```typescript
import type { Rules } from '@/components/base/Form/type'

export const rules: Rules = {
  // 必填字段（basic标签页）- 只有必填字段才添加到rules中
  brandName: [
    { type: 'string', required: true, message: '请输入品牌名称', trigger: 'blur' },
    { max: 100, message: '品牌名称长度不能超过100个字符', trigger: 'blur' },
  ],
  status: [
    { type: 'string', required: true, message: '请选择状态', trigger: 'change' },
  ],
}

// 重要说明：
// 1. 🚨 选填字段不要放入rules中（会自动变成必填项）
// 2. 只有必填字段才添加到rules，并设置required: true
// 3. id、brandCode、createTime、updateTime等自动生成字段不需要验证规则
// 4. 每个字段都必须指定type属性，类型参考对应的Model定义
```

#### 关键规范
- **核心原则**：只有必填字段才添加到rules中，选填字段不要放入
- **字段类型**：每个字段都必须指定type属性
- **触发时机**：blur（失焦）或change（变化）
- **自动生成字段**：无需验证规则

### 4. 业务服务 (`service/{ModuleName}SearchService.ts`)

#### 基本结构
```typescript
import type { BrandForm, BrandPageParam, BrandResult } from '@/api/business/brand/model/brand-ex'
import { brandApi } from '@/api/business/brand/brand-api'
import { ListCrudService } from '@/service/composite/ListCrudService'
import { provide } from 'vue'

export const BRAND_SEARCH_KEY = Symbol('BRAND_SEARCH_KEY')

/**
 * 品牌服务
 * 提供品牌相关的业务逻辑和数据管理
 */
export class BrandSearchService extends ListCrudService<BrandResult, BrandForm, BrandPageParam> {
  constructor() {
    // 初始化服务
    super(
      '品牌信息', // 业务名称
      {
        // 列表相关API
        queryList: brandApi.brandList,
        delete: brandApi.deleteBrand,
        // 表单相关API
        getDetail: brandApi.brandDetail,
        add: brandApi.addBrand,
        update: brandApi.updateBrand,
      },
      {
        labelKey: 'brandName',
      },
    )
  }

  // ---------------------------- 提供服务 ----------------------------
  provide() {
    provide(BRAND_SEARCH_KEY, this)
  }
}
```

#### 关键规范
- **继承基类**：继承`ListCrudService`，提供完整的CRUD功能
- **泛型参数**：`<ResultType, FormType, QueryType>`
- **构造函数**：传入业务名称、API对象映射、配置选项
- **API映射**：包含queryList、delete、getDetail、add、update
- **配置选项**：labelKey指定显示字段名称
- **依赖注入**：提供provide方法用于依赖注入

### 5. 导出文件 (`index.ts`)

#### 基本结构
```typescript
import BrandSearch from './BrandSearch.vue'
import { BrandSearchService } from './service/BrandSearchService'

export { BrandSearch, BrandSearchService }
```

#### 关键规范
- **统一导出**：导出主组件和服务类
- **命名导出**：使用具名导出而非默认导出
- **类型支持**：确保TypeScript类型正确导出

## 数据流规范

### 1. 标准SearchSelect流程
```
初始化: 主组件 → 创建Service → 加载列表数据 → 渲染选择器
选择: 用户选择 → 更新selectedKey → 触发v-model更新
新增: 点击新增 → 打开弹窗 → 填写表单 → 提交 → 刷新列表
编辑: 点击编辑 → 获取详情 → 打开弹窗 → 修改表单 → 提交 → 刷新列表
删除: 点击删除 → 确认删除 → 调用API → 刷新列表
```

### 2. 服务状态管理
```typescript
// ListCrudService提供的标准状态
{
  // 列表状态
  listData: [], // 选项数据
  selectedKey: null, // 当前选中值
  labelKey: 'name', // 显示字段
  valueKey: 'id', // 值字段
  placeholder: '请选择...', // 占位符

  // 表单状态
  formOpen: false, // 弹窗显示状态
  formTitle: '', // 弹窗标题
  formData: {}, // 表单数据
  formRef: null, // 表单引用

  // 方法
  openAddForm: () => void, // 打开新增表单
  openEditForm: (id) => void, // 打开编辑表单
  deleteEntity: (id) => void, // 删除实体
  submitFormAndRefresh: () => void, // 提交表单并刷新
  closeForm: () => void, // 关闭表单
}
```

## 使用规范

### 1. 基础使用
```vue
<template>
  <BrandSearch v-model="formData.brandId" />
</template>
```

### 2. 带权限控制
```vue
<template>
  <BrandSearch
    v-model="formData.brandId"
    :allow-add="true"
    :allow-edit="true"
    :allow-delete="false"
  />
</template>
```

### 3. 透传属性
```vue
<template>
  <BrandSearch
    v-model="formData.brandId"
    placeholder="请选择品牌"
    :disabled="readonly"
    size="large"
  />
</template>
```

## 权限控制规范

### 1. 组件级权限
```typescript
// Props定义
interface Props {
  allowAdd?: boolean // 是否允许新增
  allowEdit?: boolean // 是否允许编辑
  allowDelete?: boolean // 是否允许删除
}
```

### 2. 权限传递
```vue
<template>
  <SearchSelect
    :on-add="allowAdd ? () => service.openAddForm?.() : undefined"
    :on-edit="allowEdit ? (value: number) => service.openEditForm?.(value) : undefined"
    :on-delete="allowDelete ? (value: number) => service.deleteEntity?.(value) : undefined"
  />
</template>
```

### 3. 权限验证
```typescript
// 在服务中可以添加权限验证
async openAddForm() {
  if (!this.hasPermission('add')) {
    ElMessage.error('无权限进行此操作')
    return
  }
  // 继续执行新增逻辑
}
```

## 样式规范

### 1. 表单布局
```vue
<!-- 默认2列布局 -->
<Grid :cols="2" :responsive="false">
  <GridCol>
    <FormItem label="字段1" name="field1">
      <InputText />
    </FormItem>
  </GridCol>
  <GridCol>
    <FormItem label="字段2" name="field2">
      <InputText />
    </FormItem>
  </GridCol>
</Grid>
```

### 2. 标签页设计
```vue
<!-- 建议2-4个标签页 -->
<BaseTabList>
  <BaseTab value="basic">📋 基本信息</BaseTab>
  <BaseTab value="detail">🏷️ 详细信息</BaseTab>
  <BaseTab value="config">⚙️ 配置选项</BaseTab>
</BaseTabList>
```

### 3. 弹窗尺寸
```vue
<!-- 根据表单复杂度调整宽度 -->
<Dialog width="800px">  <!-- 简单表单 -->
<Dialog width="1000px"> <!-- 复杂表单 -->
<Dialog width="1200px"> <!-- 多标签页表单 -->
```

## 错误处理规范

### 1. 表单验证错误
```typescript
// 服务中的错误处理
async submitFormAndRefresh() {
  try {
    if (this.formRef.value) {
      const res = await this.formRef.value.submit()
      this.updateFormData(res.values)
    }
    await this.submitForm()
    this.refreshList()
    this.closeForm()
  } catch (error) {
    console.log('表单验证失败:', error)
    // 表单验证失败时不关闭弹窗
  }
}
```

### 2. API请求错误
```typescript
// ListCrudService自动处理常见错误
// 网络错误、服务器错误、权限错误等
// 如需自定义错误处理，可重写相关方法
```

## 性能优化规范

### 1. 列表数据缓存
```typescript
// 在服务中实现缓存机制
private _cachedList: BrandResult[] = []

async loadList() {
  if (this._cachedList.length > 0) {
    return this._cachedList
  }

  const result = await this.queryList()
  this._cachedList = result
  return result
}
```

### 2. 懒加载
```typescript
// 大数据量时使用懒加载
const BrandSearchModal = defineAsyncComponent(() =>
  import('./component/BrandSearchModal.vue')
)
```

## 代码质量规范

### 1. TypeScript规范
- 所有API接口必须有类型定义
- 使用泛型提高代码复用性
- 避免使用`any`类型
- 导入类型使用`type`关键字

### 2. 组件规范
- 使用`<script setup>`语法
- Props使用interface定义
- 组件名使用PascalCase
- 事件使用camelCase

### 3. 注释规范
```typescript
/**
 * 品牌搜索选择器服务
 * 提供品牌列表查询、新增、编辑、删除功能
 */
export class BrandSearchService extends ListCrudService<BrandResult, BrandForm, BrandPageParam> {
  // 实现细节
}
```

## 开发流程建议

1. **确定业务需求**：明确需要选择的实体类型和操作权限
2. **创建API模型**：定义Result、Form、PageParam类型
3. **实现服务类**：继承ListCrudService，配置API映射
4. **开发主组件**：实现选择器主界面和权限控制
5. **开发弹窗组件**：实现表单弹窗和标签页布局
6. **配置验证规则**：只对必填字段设置验证
7. **创建导出文件**：统一导出组件和服务
8. **测试完整流程**：验证选择、新增、编辑、删除功能
9. **优化用户体验**：确保加载状态、错误提示正确显示

## 最佳实践

### 1. 服务复用
```typescript
// 可以在多个SearchSelect中复用同一个服务
export const brandSearchService = new BrandSearchService()

// 在不同组件中使用
<BrandSearch :service="brandSearchService" />
```

### 2. 状态同步
```typescript
// 确保选择器状态与表单状态同步
watch(() => formData.brandId, (newVal) => {
  brandSearchService.selectedKey = newVal
})
```

### 3. 权限配置
```typescript
// 统一的权限配置
const permissions = {
  brand: {
    add: 'brand:add',
    edit: 'brand:edit',
    delete: 'brand:delete'
  }
}
```

## 总结

本规范为ERP业务SearchSelect组件开发提供了完整的指导方案，确保：

1. **架构一致性**：统一的组件结构和服务设计
2. **开发效率**：标准化的开发模式和代码模板
3. **功能完整性**：完整的CRUD操作和权限控制
4. **用户体验**：统一的交互模式和视觉效果
5. **可维护性**：清晰的分层和职责分离
6. **扩展性**：灵活的配置和权限机制

开发团队应严格遵循此规范，确保SearchSelect组件的高质量和一致性。
