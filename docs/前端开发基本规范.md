# 前端开发基本规范

## 🎯 核心原则

### 1. 完全分离架构
- **组件**：只负责视图渲染和依赖注入，禁止在 setup 中写业务逻辑
- **Service**：负责所有业务逻辑和状态管理
- **单向依赖**：上层组件 provide Service，子组件 inject Service

### 2. 面向对象设计
- 所有业务逻辑使用 Service 类实现
- 继承基础 Service 类复用通用能力
- 支持扩展和组合

## 📁 目录结构规范

```
/views/business/erp/supplier/
├── SupplierList.vue          # 顶层组件：只做 provide
├── components/
│   ├── SupplierForm.vue      # 子组件：只做 inject
│   └── SupplierDetail.vue    # 子组件：只做 inject
├── service/
│   └── SupplierService.ts    # 业务 Service 类
└── config/
    ├── columns.ts            # 表格列配置`
    └── rules.ts              # 表单验证规则
```

## 💻 代码规范

### 1. Service 类规范

```typescript
import { supplierApi } from '@/api/business/supplier/supplier-api'
// SupplierService.ts
import { CrudService } from '@/service/base/CrudService'
import { supplierColumns } from '../config/columns'

export class SupplierService extends CrudService<SupplierResult, SupplierForm, SupplierPageParam> {
  constructor() {
    super('供应商', supplierColumns, {
      add: supplierApi.addSupplier,
      queryPage: supplierApi.supplierPage,
      update: supplierApi.updateSupplier,
      delete: supplierApi.deleteSupplier,
      // ... 其他 API
    })
  }

  // 自定义业务方法
  approveSupplier = async (id: number): Promise<void> => {
    // 具体业务逻辑实现
  }
}

// 单例导出
export const supplierService = new SupplierService()
```

### 2. 顶层组件规范（只做 provide）

```vue
<!-- SupplierList.vue -->
<script setup lang="ts">
import { supplierService } from './service/SupplierService'

// ✅ 只负责 provide Service
supplierService.provide()
</script>

<template>
  <div class="supplier-list">
    <!-- 查询表单 -->
    <Form :ref="supplierService.tableFormRef">
      <FormItem label="供应商名称" name="supplierName">
        <InputText />
      </FormItem>
      <CustomButton @click="supplierService.onSearch">
        查询
      </CustomButton>
      <CustomButton @click="supplierService.resetQuery">
        重置
      </CustomButton>
    </Form>

    <!-- 表格和子组件 -->
    <AxTable />
    <SupplierForm />
    <SupplierDetail />
  </div>
</template>
```

### 3. 子组件规范（只做 inject）

```vue
<!-- SupplierForm.vue -->
<script setup lang="ts">
import type { SupplierService } from '../service/SupplierService'
import { CRUD_KEY } from '@/service/base/CrudService'

// ✅ 只负责 inject Service
const supplierService = inject<SupplierService>(CRUD_KEY)!
</script>

<template>
  <Dialog
    :visible="supplierService.formOpen"
    :header="supplierService.formTitle"
    @confirm="supplierService.submitFormAndRefresh"
    @cancel="supplierService.closeForm"
  >
    <Form :form-data="supplierService.formData">
      <FormItem label="供应商名称" name="supplierName">
        <InputText />
      </FormItem>
    </Form>
  </Dialog>
</template>
```

## 🔧 基础 Service 继承

```typescript
// 单表 CRUD
export class SupplierService extends CrudService<Result, Form, PageParam> {}

// 复杂表格
export class OrderService extends TableCrudService<Result, Form, PageParam> {}

// 复杂表单
export class ComplexFormService extends FormService<FormData, SubmitParam> {}

// 纯列表
export class CategoryService extends ListService<Result, QueryParam> {}
```

## 🚫 多服务场景（避免注入冲突）

```typescript
// 定义独立的注入 Key
export const ORDER_SERVICE_KEY = Symbol('orderService')
export const ORDER_ITEM_SERVICE_KEY = Symbol('orderItemService')

// Service 提供自定义 provide 方法
export class OrderService extends CrudService {
  provide(): void {
    provide(ORDER_SERVICE_KEY, this)
  }
}

// 组件中使用
const orderService = inject<OrderService>(ORDER_SERVICE_KEY)!
const orderItemService = inject<OrderItemService>(ORDER_ITEM_SERVICE_KEY)!
```

## 🧩 组件使用规范

### 1. 自动导入组件
- `/src/components` 中的组件**无需手动 import**，直接使用
- **必须使用** `/src/components/base/` 下封装的基础组件
- **禁止直接使用** 原生 PrimeVue 组件

```vue
<script setup lang="ts">
// ✅ 正确：无需导入，直接使用
// ❌ 错误：import { Button } from 'primevue'
</script>

<template>
  <!-- ✅ 使用封装的基础组件 -->
  <CustomButton>保存</CustomButton>
  <IconButton icon-type="SearchOutlined" />

  <!-- ❌ 禁止直接使用原生组件 -->
  <!-- <Button>保存</Button> -->
</template>
```

### 2. Ax 系列依赖注入组件
- **Ax 开头的组件**为依赖注入组件，具有默认实现
- 自动获取注入的 Service 数据和方法

```vue
<template>
  <!-- 自动获取 Service 的数据和操作 -->
  <AxTable />                    <!-- 表格数据和操作 -->
  <AxTableOperateButtons />      <!-- 操作按钮配置 -->
  <AxImportExcelModal />         <!-- 导入功能 -->
</template>
```

### 3. 常用基础组件

```vue
<!-- 表单组件 -->
<FormItem label="字段名" name="fieldName">
  <InputText />              <!-- 文本输入 -->
  <InputNumber />            <!-- 数字输入 -->
  <DatePicker />             <!-- 日期选择 -->
  <DictSelect />             <!-- 字典选择 -->
</FormItem>

<!-- 操作组件 -->
<CustomButton>
保存
</CustomButton>

<IconButton icon-type="EditOutlined" label="编辑" />

<!-- 布局组件 -->
<FlexRow justify="space-between">
  <Card>
    <Grid :cols="4" :gap="4">
      <GridCol>内容</GridCol>
    </Grid>
  </Card>
</FlexRow>

<!-- 数据展示 -->
<AxTable />

<DictTag />

<Descriptions>
  <DescriptionsItem label="标签">值</DescriptionsItem>
</Descriptions>
```

## ✅ 开发检查清单

### 组件层
- [ ] setup 中只有 provide/inject，无业务逻辑
- [ ] 事件处理直接调用 Service 方法
- [ ] 使用封装的基础组件，避免原生 PrimeVue 组件
- [ ] 利用自动导入，无手动 import 组件

### Service 层
- [ ] 继承合适的基础 Service 类
- [ ] 业务逻辑完整且内聚
- [ ] 使用正确的注入 Key（多服务场景）

### 架构层
- [ ] 单向依赖流：父 provide → 子 inject
- [ ] 职责分离：UI 渲染 vs 业务逻辑
- [ ] 类型安全：完整的 TypeScript 支持

## 🎯 最佳实践

### 命名规范
```typescript
// Service 类名：{业务名}Service
export class SupplierService extends CrudService {}

// 注入 Key：{业务名}_SERVICE_KEY
export const SUPPLIER_SERVICE_KEY = Symbol('supplierService')

// 实例名：{业务名}Service（小驼峰）
export const supplierService = new SupplierService()
```

### 样式规范
- **必须使用** TailwindCSS 进行样式配置
- **禁止使用** Less、Sass 等预处理器

### 性能优化
- Service 单例：避免重复创建实例
- 懒加载：大型 Service 按需加载
- 缓存策略：在 Service 层实现数据缓存

## ⚠️ 严格禁止 vs ✅ 强制要求

| ❌ 严格禁止 | ✅ 强制要求 |
|------------|------------|
| setup 中写业务逻辑 | setup 只做依赖注入 |
| props/emit 传递业务状态 | 业务逻辑全在 Service |
| 组件间直接通信 | UI 事件直接调用 Service 方法 |
| 直接使用原生 PrimeVue 组件 | 使用封装的基础组件 |
| 手动导入 components 下组件 | 利用组件自动导入功能 |
| 使用 CSS、SCSS、Less | 统一使用 TailwindCSS |

通过遵循这些规范，确保代码的**可维护性**、**可扩展性**和**团队协作效率** 🎉
