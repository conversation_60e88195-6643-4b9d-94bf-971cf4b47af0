import type { ResponseModel } from '@/api/base-model/response-model'
import type { MenuModel } from '@/api/system/menu/model/menu'
import type { MenuQueryParam, MenuResult, MenuTreeResult, RequestUrlResult } from './model/menu-ex'
import { getRequest, postRequest } from '@/lib/http-request'

export const menuApi = {

  // 查询菜单列表（对应后端 /menu/query）
  queryMenuListALl: (): Promise<ResponseModel<MenuResult[]>> => {
    return getRequest<ResponseModel<MenuResult[]>>('/menu/query')
  },

  // 查询菜单详情（对应后端 /menu/detail/{menuId}）
  getMenuDetail: (menuId: number): Promise<ResponseModel<MenuResult>> => {
    return getRequest<ResponseModel<MenuResult>>(`/menu/detail/${menuId}`)
  },

  // 查询菜单树（对应后端 /menu/tree）
  queryMenuTree: (onlyMenu: boolean): Promise<ResponseModel<MenuTreeResult[]>> => {
    return getRequest<ResponseModel<MenuTreeResult[]>>(`/menu/tree`, { onlyMenu })
  },

  // 添加菜单（对应后端 /menu/add）
  addMenu: (param: MenuModel): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, MenuModel>('/menu/add', param)
  },

  // 更新菜单（对应后端 /menu/update）
  updateMenu: (param: MenuModel): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, MenuModel>('/menu/update', param)
  },

  // 批量删除菜单（对应后端 /menu/batchDelete）
  batchDeleteMenu: (menuIdList: number[]): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>('/menu/batchDelete', { menuIdList: menuIdList.join(',') })
  },

  // 获取所有请求路径（对应后端 /menu/auth/url）
  getAuthUrlList: (): Promise<ResponseModel<RequestUrlResult[]>> => {
    return getRequest<ResponseModel<RequestUrlResult[]>>('/menu/auth/url')
  },

  // 带参数查询菜单列表（对应后端 /menu/list）
  queryMenuList: (param: MenuQueryParam): Promise<ResponseModel<MenuResult[]>> => {
    return postRequest<ResponseModel<MenuResult[]>, MenuQueryParam>('/menu/list', param)
  },
}
