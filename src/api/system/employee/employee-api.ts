import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  EmployeeAddForm,
  EmployeeBatchUpdateDepartmentForm,
  EmployeeQueryParam,
  EmployeeResult,
  EmployeeUpdateAvatarForm,
  EmployeeUpdateCenterForm,
  EmployeeUpdateForm,
  EmployeeUpdatePasswordForm,
} from './model/employee-ex'
import { getRequest, postRequest } from '@/lib/http-request'

export const employeeApi = {

  // 分页查询员工（对应后端 /employee/query）
  queryEmployeePage: (param: EmployeeQueryParam): Promise<ResponseModel<PageResult<EmployeeResult>>> => {
    return postRequest<ResponseModel<PageResult<EmployeeResult>>, EmployeeQueryParam>('/employee/query', param)
  },

  // 查询所有员工列表（对应后端 /employee/queryAll）
  queryEmployeeList: (disabledFlag?: boolean): Promise<ResponseModel<EmployeeResult[]>> => {
    const params = disabledFlag !== undefined ? { disabledFlag } : {}
    return getRequest<ResponseModel<EmployeeResult[]>>('/employee/queryAll', params)
  },

  // 根据部门ID查询员工（对应后端 /employee/getAllEmployeeByDepartmentId/{departmentId}）
  queryEmployeeByDepartmentId: (departmentId: number): Promise<ResponseModel<EmployeeResult[]>> => {
    return getRequest<ResponseModel<EmployeeResult[]>>(`/employee/getAllEmployeeByDepartmentId/${departmentId}`, {})
  },

  // 添加员工（对应后端 /employee/add）
  addEmployee: (param: EmployeeAddForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, EmployeeAddForm>('/employee/add', param)
  },

  // 更新员工信息（对应后端 /employee/update）
  updateEmployee: (param: EmployeeUpdateForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, EmployeeUpdateForm>('/employee/update', param)
  },

  // 更新员工个人中心信息（对应后端 /employee/update/center）
  updateEmployeeCenter: (param: EmployeeUpdateCenterForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, EmployeeUpdateCenterForm>('/employee/update/center', param)
  },

  // 更新员工头像（对应后端 /employee/update/avatar）
  updateEmployeeAvatar: (param: EmployeeUpdateAvatarForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, EmployeeUpdateAvatarForm>('/employee/update/avatar', param)
  },

  // 批量删除员工（对应后端 /employee/update/batch/delete）
  batchDeleteEmployee: (employeeIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/employee/update/batch/delete', employeeIdList)
  },

  // 批量调整员工部门（对应后端 /employee/update/batch/department）
  batchUpdateEmployeeDepartment: (param: EmployeeBatchUpdateDepartmentForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, EmployeeBatchUpdateDepartmentForm>('/employee/update/batch/department', param)
  },

  // 更新员工禁用/启用状态（对应后端 /employee/update/disabled/{employeeId}）
  updateEmployeeDisabled: (employeeId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/employee/update/disabled/${employeeId}`, {})
  },

  // 重置员工密码（对应后端 /employee/update/password/reset/{employeeId}）
  resetEmployeePassword: (employeeId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/employee/update/password/reset/${employeeId}`, {})
  },

  // 修改密码（对应后端 /employee/update/password）
  updateEmployeePassword: (param: EmployeeUpdatePasswordForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, EmployeeUpdatePasswordForm>('/employee/update/password', param)
  },

  // 获取密码复杂度配置（对应后端 /employee/getPasswordComplexityEnabled）
  getPasswordComplexityEnabled: (): Promise<ResponseModel<boolean>> => {
    return getRequest<ResponseModel<boolean>>('/employee/getPasswordComplexityEnabled', {})
  },
}
