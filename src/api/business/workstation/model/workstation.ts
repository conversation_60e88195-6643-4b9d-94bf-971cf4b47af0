import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 生产工作站信息类型定义
 */
export interface WorkstationModel extends BaseModel {
  id?: number // 生产工作站信息ID

  stationCode?: string // 工作站编码

  stationName?: string // 工作站名称

  stationType?: string // 字典 | 工作站类型 workstation_type：cutting-切割工位，printing-印刷工位，folding-折叠工位，gluing-胶合工位，packing-包装工位，inspection-检测工位，manual-手工工位

  equipmentId?: number // 设备ID | mfg_equipment的id

  status?: string // 字典 | 工作站状态 workstation_status：available-可用，busy-忙碌，maintenance-维护中，fault-故障，stopped-停机

  capacity?: number // 产能(件/小时)

  location?: string // 工作站位置

  operatorId?: number // 操作员ID | t_employee的employee_id

  remark?: string // 备注

  workshopId?: number // 车间ID | mfg_workshop的id

  workshopName?: string // 车间名称

}
