import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 生产工单基础信息类型定义
 */
export interface ProductionOrderModel extends BaseModel {
  id?: number // 生产工单基础信息ID

  orderNo?: string // 工单编号

  planId?: number // 生产计划ID | mfg_production_plan的id

  orderType?: string // 字典 | 工单类型 production_order_type：normal-普通工单，planned-计划工单，supplement-补充工单

  startDate?: string // 开始日期

  endDate?: string // 结束日期

  priority?: string // 字典 | 优先级 production_priority：low-低，normal-普通，high-高，urgent-紧急

  processRouteId?: number // 工艺路线ID | mfg_process_route的id

  processRouteName?: string // 工艺路线名称

  operatorId?: number // 操作人ID

  operatorName?: string // 操作人姓名

  productId?: number // 商品ID | erp_product表的id

  productCode?: string // 商品编码

  productName?: string // 商品名称

  skuId?: number // SKU ID | erp_sku表的id

  skuCode?: string // SKU编码

  specSummary?: string // 规格摘要

  unit?: string // 单位

  quantity?: number // 生产数量

  status?: string // 字典 | 工单状态 production_order_status：pending-待开始，processing-进行中，completed-已完成，cancelled-已取消

  remark?: string // 备注

}
