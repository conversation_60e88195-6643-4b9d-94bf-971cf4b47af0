import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type { WorkshopForm, WorkshopPageParam, WorkshopQueryParam, WorkshopResult } from './model/workshop-ex'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const workshopApi = {

  // 分页查询生产车间信息
  workshopPage: (param: WorkshopPageParam): Promise<ResponseModel<PageResult<WorkshopResult>>> => {
    return postRequest<ResponseModel<PageResult<WorkshopResult>>, WorkshopQueryParam>('/workshop/workshopPage', param)
  },
  // 列表查询生产车间信息
  workshopList: (param: WorkshopQueryParam): Promise<ResponseModel<WorkshopForm[]>> => {
    return postRequest<ResponseModel<WorkshopForm[]>, WorkshopQueryParam>('/workshop/workshopList', param)
  },
  // 获取生产车间信息详情
  workshopDetail: (workshopId: number): Promise<ResponseModel<WorkshopResult>> => {
    return getRequest<ResponseModel<WorkshopResult>>(`/workshop/workshopDetail/${workshopId}`, {})
  },
  // 添加生产车间信息
  addWorkshop: (param: WorkshopForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, WorkshopForm>('/workshop/addWorkshop', param)
  },
  // 更新生产车间信息
  updateWorkshop: (param: WorkshopForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, WorkshopForm>('/workshop/updateWorkshop', param)
  },
  // 删除生产车间信息
  deleteWorkshop: (workshopId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/workshop/deleteWorkshop/${workshopId}`, {})
  },
  // 批量删除生产车间信息
  batchDeleteWorkshop: (workshopIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/workshop/batchDeleteWorkshop', workshopIdList)
  },

  // 导入生产车间信息
  importWorkshop: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/workshop/importWorkshopExcel', file)
  },
  // 导出生产车间信息
  exportWorkshop: (param?: WorkshopQueryParam): void => {
    return getDownload('/workshop/exportWorkshopExcel', param || {})
  },
}
