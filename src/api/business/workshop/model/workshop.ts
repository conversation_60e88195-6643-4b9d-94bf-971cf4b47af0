import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 生产车间信息类型定义
 */
export interface WorkshopModel extends BaseModel {
  id?: number // 生产车间信息ID

  workshopCode?: string // 车间编码

  workshopName?: string // 车间名称

  workshopType?: string // 字典 | 车间类型 workshop_type：production-生产车间，assembly-装配车间，packing-包装车间，inspection-检测车间，warehouse-仓储车间，maintenance-维护车间

  status?: string // 字典 | 车间状态 workshop_status：active-活跃，inactive-停用，maintenance-维护中，upgrading-升级中，closed-关闭

  managerId?: number // 车间主管ID | t_employee的employee_id

  managerName?: string // 车间主管姓名

  contactPhone?: string // 联系电话

  remark?: string // 备注

}
