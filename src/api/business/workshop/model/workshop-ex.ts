import type { PageParam } from '@/api/base-model/page-model'
import type { WorkshopModel } from './workshop'

/**
 * 生产车间信息查询参数
 */
export interface WorkshopQueryParam extends WorkshopModel {

  createTimeFrom?: string // 创建时间始

  createTimeTo?: string // 创建时间至

  updateTimeFrom?: string // 更新时间始

  updateTimeTo?: string // 更新时间至
}

/**
 * 生产车间信息分页查询参数
 */
export interface WorkshopPageParam extends WorkshopQueryParam {
  pageParam: PageParam
}

/**
 * 生产车间信息表单类型
 */
export interface WorkshopForm extends WorkshopModel {

}

/**
 * 生产车间信息查询结果类
 */
export interface WorkshopResult extends WorkshopModel {

}
