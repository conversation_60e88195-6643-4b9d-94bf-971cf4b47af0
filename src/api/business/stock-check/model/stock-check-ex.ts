import type { PageParam } from '@/api/base-model/page-model'
import type { StockCheckModel } from './'

/**
 * 库存盘点（精简版）查询参数
 */
export interface StockCheckQueryParam extends StockCheckModel {

  createTimeFrom?: string // 创建时间始

  createTimeTo?: string // 创建时间至

  updateTimeFrom?: string // 更新时间始

  updateTimeTo?: string // 更新时间至
}

/**
 * 库存盘点（精简版）分页查询参数
 */
export interface StockCheckPageParam extends StockCheckQueryParam {
  pageParam: PageParam
}

/**
 * 库存盘点（精简版）表单类型
 */
export interface StockCheckForm extends StockCheckModel {

}

/**
 * 库存盘点（精简版）查询结果类
 */
export interface StockCheckResult extends StockCheckModel {

}
