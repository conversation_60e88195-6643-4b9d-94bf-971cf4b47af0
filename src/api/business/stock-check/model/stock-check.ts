import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 库存盘点类型定义
 */
export interface StockCheckModel extends BaseModel {
  id?: number // 库存盘点ID

  checkNo?: string // 盘点单号

  warehouseId?: number // 仓库ID | erp_warehouse表的id

  warehouseCode?: string // 仓库编码

  warehouseName?: string // 仓库名称

  checkStatus?: string // 字典 | 盘点状态 check_status：DRAFT-草稿，IN_PROGRESS-盘点中，COMPLETED-已完成，CANCELLED-已取消

  totalItems?: number // 盘点商品总数

  checkedItems?: number // 已盘点商品数

  checkerId?: number // 盘点人ID | t_employee表的employee_id

  checkerName?: string // 盘点人姓名

  remark?: string // 备注

}
