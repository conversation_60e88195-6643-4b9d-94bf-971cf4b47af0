import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type { StockCheckForm, StockCheckPageParam, StockCheckQueryParam, StockCheckResult } from './model/stock-check-ex'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const stockCheckApi = {

  // 分页查询库存盘点
  stockCheckPage: (param: StockCheckPageParam): Promise<ResponseModel<PageResult<StockCheckResult>>> => {
    return postRequest<ResponseModel<PageResult<StockCheckResult>>, StockCheckQueryParam>('/stockCheck/stockCheckPage', param)
  },
  // 列表查询库存盘点
  stockCheckList: (param: StockCheckQueryParam): Promise<ResponseModel<StockCheckForm[]>> => {
    return postRequest<ResponseModel<StockCheckForm[]>, StockCheckQueryParam>('/stockCheck/stockCheckList', param)
  },
  // 获取库存盘点详情
  stockCheckDetail: (stockCheckId: number): Promise<ResponseModel<StockCheckResult>> => {
    return getRequest<ResponseModel<StockCheckResult>>(`/stockCheck/stockCheckDetail/${stockCheckId}`, {})
  },
  // 添加库存盘点
  addStockCheck: (param: StockCheckForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, StockCheckForm>('/stockCheck/addStockCheck', param)
  },
  // 更新库存盘点
  updateStockCheck: (param: StockCheckForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, StockCheckForm>('/stockCheck/updateStockCheck', param)
  },
  // 删除库存盘点
  deleteStockCheck: (stockCheckId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/stockCheck/deleteStockCheck/${stockCheckId}`, {})
  },
  // 批量删除库存盘点
  batchDeleteStockCheck: (stockCheckIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/stockCheck/batchDeleteStockCheck', stockCheckIdList)
  },

  // 导入库存盘点
  importStockCheck: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/stockCheck/importStockCheckExcel', file)
  },
  // 导出库存盘点
  exportStockCheck: (param?: StockCheckQueryParam): void => {
    return getDownload('/stockCheck/exportStockCheckExcel', param || {})
  },
}
