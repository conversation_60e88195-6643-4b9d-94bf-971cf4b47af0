import type { PageParam } from '@/api/base-model/page-model'
import type { ProductFormParam, ProductQueryParam, ProductResult } from '@/api/business/product/model/product-form-model'

/**
 * 物料查询参数
 * 基于ProductQueryParam，专注于物料管理场景
 */
export interface MaterialQueryParam extends ProductQueryParam {
  // 继承所有Product查询参数
  // 物料特有的查询条件可以在这里扩展
  
  // 物料类型筛选（原材料、半成品等）
  materialType?: string
  
  // 是否危险品
  isDangerous?: boolean
  
  // 存储条件要求
  storageCondition?: string
}

/**
 * 物料分页查询参数
 */
export interface MaterialPageParam extends MaterialQueryParam {
  pageParam: PageParam
}

/**
 * 物料表单类型
 * 基于ProductFormParam，专注于物料管理场景
 */
export interface MaterialForm extends ProductFormParam {
  // 继承所有Product表单参数
  // 物料特有的表单字段可以在这里扩展
  
  // 确保是原材料
  isRawMaterial: true
  
  // 物料特有属性
  materialType?: string // 物料类型
  isDangerous?: boolean // 是否危险品
  storageCondition?: string // 存储条件
  shelfLife?: number // 保质期（天）
  safetyStock?: number // 安全库存
}

/**
 * 物料查询结果类
 * 基于ProductResult，专注于物料管理场景
 */
export interface MaterialResult extends ProductResult {
  // 继承所有Product结果字段
  // 物料特有的结果字段可以在这里扩展
  
  // 物料特有属性
  materialType?: string // 物料类型
  isDangerous?: boolean // 是否危险品
  storageCondition?: string // 存储条件
  shelfLife?: number // 保质期（天）
  safetyStock?: number // 安全库存
  
  // 库存相关信息（如果需要）
  currentStock?: number // 当前库存
  availableStock?: number // 可用库存
  reservedStock?: number // 预留库存
}
