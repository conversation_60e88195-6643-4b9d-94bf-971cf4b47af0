import type { PageParam } from '@/api/base-model/page-model'
import type { StockCheckItemModel } from './stock-check-item'

/**
 * 库存盘点明细（精简版）查询参数
 */
export interface StockCheckItemQueryParam extends StockCheckItemModel {

  createTimeFrom?: string // 创建时间始

  createTimeTo?: string // 创建时间至

  updateTimeFrom?: string // 更新时间始

  updateTimeTo?: string // 更新时间至
}

/**
 * 库存盘点明细（精简版）分页查询参数
 */
export interface StockCheckItemPageParam extends StockCheckItemQueryParam {
  pageParam: PageParam
}

/**
 * 库存盘点明细（精简版）表单类型
 */
export interface StockCheckItemForm extends StockCheckItemModel {

}

/**
 * 库存盘点明细（精简版）查询结果类
 */
export interface StockCheckItemResult extends StockCheckItemModel {

}
