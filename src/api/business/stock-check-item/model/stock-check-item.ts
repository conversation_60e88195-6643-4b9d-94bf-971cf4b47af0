import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 库存盘点明细（精简版）类型定义
 */
export interface StockCheckItemModel extends BaseModel {
  id?: number // 库存盘点明细（精简版）ID

  checkId?: number // 盘点ID | erp_stock_check表的id

  checkNo?: string // 盘点单号

  lineNo?: number // 行号

  productId?: number // 商品ID | erp_product表的id

  productCode?: string // 商品编码

  productName?: string // 商品名称

  skuId?: number // SKU ID | erp_sku表的id

  skuCode?: string // SKU编码

  specSummary?: string // 规格摘要

  unit?: string // 单位

  bookStock?: number // 账面库存

  actualStock?: number // 实盘库存

  differenceStock?: number // 差异数量 (实盘-账面)

  differenceType?: string // 字典 | 差异类型 difference_type：NORMAL-正常，PROFIT-盘盈，LOSS-盘亏

  isChecked?: boolean // 是否已盘点：FALSE-否，TRUE-是

  checkerId?: number // 盘点人ID | t_employee表的employee_id

  checkerName?: string // 盘点人姓名

  differenceReason?: string // 字典 | 差异原因 difference_reason：DAMAGE-损坏，THEFT-盗失，EXPIRED-过期，MISCOUNT-计数错误，SYSTEM_ERROR-系统错误，OTHER-其他

  remark?: string // 备注

}
