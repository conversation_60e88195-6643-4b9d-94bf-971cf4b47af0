import type {BaseModel} from '@/api/base-model/base-model'

/**
 * 物料确认主表类型定义
 */
export interface MaterialRequestModel extends BaseModel {
  id?: number // 物料确认主表ID

  requestNo?: string // 确认单号

  skuId?: number // SKU ID | erp_sku的id

  skuCode?: string // SKU编码

  skuName?: string // SKU名称

  requiredQuantity?: number // 需求数量

  unit?: string // 计量单位

  requestDate?: string // 需求日期

  status?: string // 字典 | 确认状态 material_request_status：pending-待确认，confirmed-已确认，rejected-已拒绝

  remark?: string // 备注

}
