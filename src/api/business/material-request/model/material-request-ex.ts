import type {PageParam} from '@/api/base-model/page-model'
import type {MaterialRequestModel} from './'

/**
 * 物料确认主表查询参数
 */
export interface MaterialRequestQueryParam extends MaterialRequestModel {

  requestDateFrom?: string // 需求日期始

  requestDateTo?: string // 需求日期至

  createTimeFrom?: string // 创建时间始

  createTimeTo?: string // 创建时间至

  updateTimeFrom?: string // 更新时间始

  updateTimeTo?: string // 更新时间至
}

/**
 * 物料确认主表分页查询参数
 */
export interface MaterialRequestPageParam extends MaterialRequestQueryParam {
  pageParam: PageParam
}

/**
 * 物料确认主表表单类型
 */
export interface MaterialRequestForm extends MaterialRequestModel {

}

/**
 * 物料确认主表查询结果类
 */
export interface MaterialRequestResult extends MaterialRequestModel {

}
