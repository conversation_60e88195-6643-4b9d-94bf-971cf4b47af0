import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type { MaterialRequestForm, MaterialRequestPageParam, MaterialRequestQueryParam, MaterialRequestResult } from './model/material-request-ex'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const materialRequestApi = {

  // 分页查询物料确认主表
  materialRequestPage: (param: MaterialRequestPageParam): Promise<ResponseModel<PageResult<MaterialRequestResult>>> => {
    return postRequest<ResponseModel<PageResult<MaterialRequestResult>>, MaterialRequestQueryParam>('/materialRequest/materialRequestPage', param)
  },
  // 列表查询物料确认主表
  materialRequestList: (param: MaterialRequestQueryParam): Promise<ResponseModel<MaterialRequestForm[]>> => {
    return postRequest<ResponseModel<MaterialRequestForm[]>, MaterialRequestQueryParam>('/materialRequest/materialRequestList', param)
  },
  // 获取物料确认主表详情
  materialRequestDetail: (materialRequestId: number): Promise<ResponseModel<MaterialRequestResult>> => {
    return getRequest<ResponseModel<MaterialRequestResult>>(`/materialRequest/materialRequestDetail/${materialRequestId}`, {})
  },
  // 添加物料确认主表
  addMaterialRequest: (param: MaterialRequestForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, MaterialRequestForm>('/materialRequest/addMaterialRequest', param)
  },
  // 更新物料确认主表
  updateMaterialRequest: (param: MaterialRequestForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, MaterialRequestForm>('/materialRequest/updateMaterialRequest', param)
  },
  // 删除物料确认主表
  deleteMaterialRequest: (materialRequestId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/materialRequest/deleteMaterialRequest/${materialRequestId}`, {})
  },
  // 批量删除物料确认主表
  batchDeleteMaterialRequest: (materialRequestIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/materialRequest/batchDeleteMaterialRequest', materialRequestIdList)
  },

  // 导入物料确认主表
  importMaterialRequest: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/materialRequest/importMaterialRequestExcel', file)
  },
  // 导出物料确认主表
  exportMaterialRequest: (param?: MaterialRequestQueryParam): void => {
    return getDownload('/materialRequest/exportMaterialRequestExcel', param || {})
  },
}
