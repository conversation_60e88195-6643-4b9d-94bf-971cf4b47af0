import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type { BomForm, BomPageParam, BomQueryParam, BomResult } from './model/bom-ex'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const bomApi = {

  // 分页查询物料清单BOM
  bomPage: (param: BomPageParam): Promise<ResponseModel<PageResult<BomResult>>> => {
    return postRequest<ResponseModel<PageResult<BomResult>>, BomQueryParam>('/bom/bomPage', param)
  },
  // 列表查询物料清单BOM
  bomList: (param: BomQueryParam): Promise<ResponseModel<BomForm[]>> => {
    return postRequest<ResponseModel<BomForm[]>, BomQueryParam>('/bom/bomList', param)
  },
  // 获取物料清单BOM详情
  bomDetail: (bomId: number): Promise<ResponseModel<BomResult>> => {
    return getRequest<ResponseModel<BomResult>>(`/bom/bomDetail/${bomId}`, {})
  },
  // 添加物料清单BOM
  addBom: (param: BomForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, BomForm>('/bom/addBom', param)
  },
  // 更新物料清单BOM
  updateBom: (param: BomForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, BomForm>('/bom/updateBom', param)
  },
  // 删除物料清单BOM
  deleteBom: (bomId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/bom/deleteBom/${bomId}`, {})
  },
  // 批量删除物料清单BOM
  batchDeleteBom: (bomIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/bom/batchDeleteBom', bomIdList)
  },

  // 导入物料清单BOM
  importBom: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/bom/importBomExcel', file)
  },
  // 导出物料清单BOM
  exportBom: (param?: BomQueryParam): void => {
    return getDownload('/bom/exportBomExcel', param || {})
  },
}
