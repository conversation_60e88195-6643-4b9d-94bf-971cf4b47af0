import type {BaseModel} from '@/api/base-model/base-model'

/**
 * 物料清单BOM类型定义
 */
export interface BomModel extends BaseModel {
  id?: number // 物料清单BOMID

  parentSkuId?: number // 成品SKU ID | erp_sku的id

  childSkuId?: number // 组件SKU ID | erp_sku的id

  quantity?: number // 用量数量

  unit?: string // 计量单位

  sortOrder?: number // 排序号

  status?: string // 字典 | BOM状态 bom_status：0-停用，1-启用

  remark?: string // 备注

}
