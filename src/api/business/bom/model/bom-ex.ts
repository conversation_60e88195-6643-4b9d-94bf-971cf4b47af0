import type {PageParam} from '@/api/base-model/page-model'
import type {BomModel} from './'

/**
 * 物料清单BOM查询参数
 */
export interface BomQueryParam extends BomModel {

  createTimeFrom?: string // 创建时间始

  createTimeTo?: string // 创建时间至

  updateTimeFrom?: string // 更新时间始

  updateTimeTo?: string // 更新时间至
}

/**
 * 物料清单BOM分页查询参数
 */
export interface BomPageParam extends BomQueryParam {
  pageParam: PageParam
}

/**
 * 物料清单BOM表单类型
 */
export interface BomForm extends BomModel {

}

/**
 * 物料清单BOM查询结果类
 */
export interface BomResult extends BomModel {

}
