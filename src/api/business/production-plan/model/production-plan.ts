import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 生产计划表类型定义
 */
export interface ProductionPlanModel extends BaseModel {
  id?: number // 生产计划表ID

  planNo?: string // 计划编号

  planName?: string // 计划名称

  planStartDate?: string // 计划开始日期

  planEndDate?: string // 计划结束日期

  periodType?: string // 字典 | 计划周期类型 production_plan_period_type：day-日计划，week-周计划，month-月计划

  salesOrderId?: number // 关联销售订单ID | erp_sales_order表的id，为空表示库存补充计划

  status?: string // 字典 | 计划状态 production_plan_status：draft-草稿，confirmed-已确认，executing-执行中，completed-已完成，cancelled-已取消

  remark?: string // 备注

}
