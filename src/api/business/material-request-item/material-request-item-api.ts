import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type { MaterialRequestItemForm, MaterialRequestItemPageParam, MaterialRequestItemQueryParam, MaterialRequestItemResult } from './model/material-request-item-ex'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const materialRequestItemApi = {

  // 分页查询物料确认明细
  materialRequestItemPage: (param: MaterialRequestItemPageParam): Promise<ResponseModel<PageResult<MaterialRequestItemResult>>> => {
    return postRequest<ResponseModel<PageResult<MaterialRequestItemResult>>, MaterialRequestItemQueryParam>('/materialRequestItem/materialRequestItemPage', param)
  },
  // 列表查询物料确认明细
  materialRequestItemList: (param: MaterialRequestItemQueryParam): Promise<ResponseModel<MaterialRequestItemForm[]>> => {
    return postRequest<ResponseModel<MaterialRequestItemForm[]>, MaterialRequestItemQueryParam>('/materialRequestItem/materialRequestItemList', param)
  },
  // 获取物料确认明细详情
  materialRequestItemDetail: (materialRequestItemId: number): Promise<ResponseModel<MaterialRequestItemResult>> => {
    return getRequest<ResponseModel<MaterialRequestItemResult>>(`/materialRequestItem/materialRequestItemDetail/${materialRequestItemId}`, {})
  },
  // 添加物料确认明细
  addMaterialRequestItem: (param: MaterialRequestItemForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, MaterialRequestItemForm>('/materialRequestItem/addMaterialRequestItem', param)
  },
  // 更新物料确认明细
  updateMaterialRequestItem: (param: MaterialRequestItemForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, MaterialRequestItemForm>('/materialRequestItem/updateMaterialRequestItem', param)
  },
  // 删除物料确认明细
  deleteMaterialRequestItem: (materialRequestItemId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/materialRequestItem/deleteMaterialRequestItem/${materialRequestItemId}`, {})
  },
  // 批量删除物料确认明细
  batchDeleteMaterialRequestItem: (materialRequestItemIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/materialRequestItem/batchDeleteMaterialRequestItem', materialRequestItemIdList)
  },

  // 导入物料确认明细
  importMaterialRequestItem: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/materialRequestItem/importMaterialRequestItemExcel', file)
  },
  // 导出物料确认明细
  exportMaterialRequestItem: (param?: MaterialRequestItemQueryParam): void => {
    return getDownload('/materialRequestItem/exportMaterialRequestItemExcel', param || {})
  },
}
