import type {BaseModel} from '@/api/base-model/base-model'

/**
 * 物料确认明细类型定义
 */
export interface MaterialRequestItemModel extends BaseModel {
  id?: number // 物料确认明细ID

  requestId?: number // 物料确认ID | mfg_material_request的id

  materialSkuId?: number // 物料SKU ID | erp_sku的id

  materialSkuCode?: string // 物料SKU编码

  materialSkuName?: string // 物料SKU名称

  requiredQuantity?: number // 需求数量

  availableQuantity?: number // 可用库存数量

  confirmedQuantity?: number // 确认数量

  unit?: string // 计量单位

  status?: string // 字典 | 明细状态 material_item_status：pending-待确认，sufficient-库存充足，insufficient-库存不足

  remark?: string // 备注

}
