<script setup lang="ts">
import type { DateRangePickerProps } from './type'
// 日期范围选择器组件
// 基于 PrimeVue 实现，支持传统from/to模式和现代defineModel模式
import DatePicker from 'primevue/datepicker'
import { ref } from 'vue'

// 定义 props，使用默认值
const props = withDefaults(defineProps<DateRangePickerProps>(), {
  placeholder: () => ['开始日期', '结束日期'],
  dateFormat: 'yyyy-mm-dd',
  class: '',
  disabled: false,
  readonly: false,
  showIcon: true,
  iconDisplay: 'input',
})

// 现代模式：支持 defineModel（可选）
const modelValue = defineModel<Date[]>()

const placeholderRef = ref('')
// 失去焦点
function handleBlur() {
  placeholderRef.value = ''
}
// 聚焦
function handleFocus() {
  placeholderRef.value = props.placeholder?.[0] || '选择日期范围'
}
</script>

<template>
  <!-- 透传 $attrs，方便外部自定义 -->
  <DatePicker
    v-model="modelValue"
    class="w-full"
    :class="props.class"
    :placeholder="placeholderRef"
    :disabled="props.disabled"
    :readonly="props.readonly"
    :show-icon="props.showIcon"
    :icon-display="props.iconDisplay"
    selection-mode="range"
    show-button-bar
    v-bind="$attrs"
    @focus="handleFocus"
    @blur="handleBlur"
    @update:model-value="(e) => {
      console.log(e);

    }"
  />
</template>
