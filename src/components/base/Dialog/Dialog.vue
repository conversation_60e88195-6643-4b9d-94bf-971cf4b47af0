<script setup lang="ts">
import Button from 'primevue/button'
import PrimeDialog from 'primevue/dialog'

defineOptions({
  inheritAttrs: false,
})

defineProps({
  showFooter: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits<{
  confirm: []
  cancel: []
}>()

const defaultAttrs = {
  modal: true,
  closable: true,
  closeOnEscape: false,
  dismissableMask: false,
  class: 'w-[40rem]',
}

function handleConfirm() {
  emit('confirm')
}

function handleCancel() {
  emit('cancel')
}

// 处理 PrimeVue Dialog 的关闭事件
function handleDialogClose(value: boolean) {
  if (!value) {
    handleCancel()
  }
}
</script>

<template>
  <PrimeDialog
    v-bind="{ ...defaultAttrs, ...$attrs }"
    @update:visible="handleDialogClose"
  >
    <template #default>
      <slot />
    </template>
    <template #footer>
      <slot v-if="showFooter" name="footer">
        <Button label="取消" @click="handleCancel" />
        <Button label="确定" @click="handleConfirm" />
      </slot>
    </template>
  </primedialog>
</template>
