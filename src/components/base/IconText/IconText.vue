<script setup lang="ts">
import type { IconTextProps } from './type'

defineOptions({
  inheritAttrs: false,
})

const {
  label = '',
  iconPosition = 'left',
  iconType = 'UserOutlined',
  spin = false,
  rotate = 0,
} = defineProps<IconTextProps>()
</script>

<template>
  <div v-bind="$attrs" class="flex items-center gap-1">
    <Icon v-if="iconPosition === 'left'" :icon-type="iconType" :spin="spin" :rotate="rotate" />
    <span>{{ label }}</span>
    <Icon v-if="iconPosition === 'right'" :icon-type="iconType" :spin="spin" :rotate="rotate" />
  </div>
</template>
