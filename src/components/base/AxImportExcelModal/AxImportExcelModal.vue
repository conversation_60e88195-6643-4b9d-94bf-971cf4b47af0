<script lang="ts" setup>
import type { Excel } from '@/service/base-old/interface/CrudInterface'
import { CRUD_KEY } from '@/service/composite/TableCrudService'
// 组件自动导入，不需要手动导入
import { inject } from 'vue'

// 获取服务实例
const service = inject<Excel>(CRUD_KEY)!

// 处理文件选择
function onFileSelect(event: { files: File[] }) {
  if (event.files && event.files.length > 0) {
    service.handleImportFile(event.files[0])
  }
}
</script>

<template>
  <Dialog
    v-model:visible="service.importModalOpen"
    header="导入数据"
    :style="{ width: '400px' }"
    modal
  >
    <div class="text-center w-full mx-auto">
      <div class="mb-4">
        <Button
          label="第一步：下载模板"
          icon="pi pi-download"
          severity="secondary"
          outlined
          @click="service.downloadTemplate"
        />
      </div>

      <div class="mb-4">
        <FileUpload
          mode="basic"
          name="file"
          accept=".xls,.xlsx"
          :max-file-size="10000000"
          :auto="false"
          :custom-upload="true"
          choose-label="第二步：选择文件"
          :choose-icon="service.hasUploadFile ? 'pi pi-check' : 'pi pi-upload'"
          :severity="service.hasUploadFile ? 'success' : 'primary'"
          @select="onFileSelect"
        />
      </div>

      <div v-if="service.hasUploadFile" class="my-2 text-primary">
        <i class="pi pi-check-circle mr-2" />
        {{ service.uploadFile?.name }}
      </div>

      <div class="mt-4">
        <Button
          label="第三步：开始导入"
          icon="pi pi-upload"
          :disabled="!service.hasUploadFile"
          @click="service.onImport"
        />
      </div>
    </div>
    <template #footer>
      <Button label="取消" severity="danger" @click="service.setImportModalOpen(false)" />
    </template>
  </Dialog>
</template>
