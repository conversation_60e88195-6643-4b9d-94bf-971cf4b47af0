<script setup lang="ts">
import type { SearchSelectProps } from './index'

const {
  placeholder = '请选择',
  options = [],
  optionLabel = '',
  optionValue = '',
  onAdd = undefined,
  onEdit = undefined,
  onDelete = undefined,
} = defineProps<SearchSelectProps>()

const modelValue = defineModel<number | string | undefined>({ default: undefined })
</script>

<template>
  <Select
    v-model="modelValue"
    :placeholder="placeholder"
    :options="options"
    :option-label="optionLabel"
    :option-value="optionValue"
    v-bind="$attrs"
  >
    <template #option="slotProps">
      <FlexRow class="w-full">
        <span>{{ slotProps.option[optionLabel] }}</span>
        <FlexRow>
          <IconAnchor
            v-if="!!onEdit"
            label=""
            icon-type="EditOutlined"
            @click="(e) => {
              e.stopPropagation()
              onEdit?.(slotProps.option[optionValue])
            }"
          />
          <IconAnchor
            v-if="!!onDelete"
            label=""
            color="red"
            icon-type="DeleteOutlined"
            @click="(e) => {
              e.stopPropagation()
              onDelete?.(slotProps.option[optionValue])
            }"
          />
        </FlexRow>
      </FlexRow>
    </template>

    <!-- 添加自定义选项按钮 -->
    <template v-if="!!onAdd" #footer>
      <div class="p-2 inline-flex w-full">
        <Button
          label="自定义 (custom)"
          fluid
          severity="secondary"
          text
          size="small"
          icon="pi pi-plus"
          class="w-full!"
          @click="() => onAdd?.()"
        />
      </div>
    </template>
  </Select>
</template>
