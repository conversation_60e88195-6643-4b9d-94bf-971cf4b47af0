<script setup lang="ts">
import { CustomLabel } from '@/components/base/CustomLabel'
import FormField from '@primevue/forms/formfield'
import { Message } from 'primevue'

type LabelType = 'ifta' | 'float' | 'none' | 'left'
interface Props {
  type?: LabelType
  forId?: string
  variant?: 'over' | 'on' | 'in'
  label?: string
  name?: string
  [key: string]: unknown
}

const { label, forId, type = 'float', variant = 'on', name } = defineProps<Props>()
</script>

<template>
  <FormField v-slot="$field" class="mt-2" :name="name" v-bind="$attrs">
    <!-- 无标签 -->
    <slot v-if="type === 'none'" v-bind="$field" />
    <!-- 左对齐 -->
    <div v-else-if="type === 'left'" class="flex items-center gap-2">
      <label :for="forId">{{ label }}</label>
      <slot v-bind="$field" />
    </div>
    <!-- 有标签 -->
    <CustomLabel v-else :label="label" :for-id="forId" :type="type" :variant="variant" v-bind="$attrs">
      <slot v-bind="$field" />
    </CustomLabel>
    <Message v-if="$field?.invalid" severity="error" size="small" variant="simple">
      {{ $field.error?.message }}
    </Message>
  </FormField>
</template>
