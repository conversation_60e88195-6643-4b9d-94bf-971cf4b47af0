<script setup lang="ts">
// 基础组件自动导入
import type { IconButtonProps } from './type'

defineOptions({
  inheritAttrs: false,
})

const {
  label = '',
  iconType = 'UserOutlined',
  spin = false,
  rotate = 0,
} = defineProps<IconButtonProps>()
</script>

<template>
  <Button v-bind="$attrs">
    <template #icon>
      <Icon :icon-type="iconType" :spin="spin" :rotate="rotate" />
    </template>
    {{ label }}
  </Button>
</template>
