<script setup lang="ts">
// 基础组件自动导入
import type { GridProps } from './index'

defineOptions({
  inheritAttrs: false,
})

const { cols = 5, gap = 4, responsive = true } = defineProps<GridProps>()

const gridClasses = computed(() => {
  const classes = ['w-full grid']

  if (responsive) {
    // 默认超大屏布局：手机1列，平板2列，桌面3列，大屏4列，超大屏5列
    classes.push('grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5')
  }
  else {
    // @unocss-include
    classes.push(`grid-cols-${cols}`)
  }

  // 添加间距
  classes.push(`gap-${gap}`)

  return classes.join(' ')
})
</script>

<template>
  <div v-bind="$attrs" :class="gridClasses">
    <slot />
  </div>
</template>
