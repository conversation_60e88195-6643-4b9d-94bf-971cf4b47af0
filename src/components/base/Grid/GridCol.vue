<script setup lang="ts">
// 基础组件自动导入
import type { GridColProps } from './index'
import { computed } from 'vue'

defineOptions({
  inheritAttrs: false,
})

const { span = 1, offset = 0, order = 0 } = defineProps<GridColProps>()

const colClasses = computed(() => {
  const classes = []

  // 列跨度（只在span > 1时才添加col-span类）
  if (span && span > 1) {
    // @unocss-include
    classes.push(`col-span-${span}`)
  }

  // 偏移
  if (offset) {
    classes.push(`col-start-${offset + 1}`)
  }

  // 排序
  if (order) {
    classes.push(`order-${order}`)
  }

  return classes.join(' ')
})
</script>

<template>
  <div v-bind="$attrs" :class="colClasses">
    <slot />
  </div>
</template>
