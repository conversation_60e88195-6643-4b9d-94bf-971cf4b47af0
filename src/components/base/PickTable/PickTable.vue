<script setup lang="ts">
import type { BaseModel } from '@/api/base-model/base-model'
import type { PickTableProps } from './type'

defineOptions({
  inheritAttrs: false,
})
const {
  rawTableData = [],
  rawTableColumn = [],
  selectedTableData = [],
  selectedTableColumn = [],
  selectItem = (_item: Record<string, unknown>) => {},
  removeItemBySource = () => {},
  removeItemByDestination = () => {},
  isItemSelected = (_item: Record<string, unknown>) => false,
} = defineProps<PickTableProps>()

// @ts-ignore
const $pcFormField = inject('$pcFormField', undefined)

// 读取/设置表单字段的当前值（selected rows）(欺骗PrimeVue Forms)
const selectedRows = computed({
  // @ts-ignore
  get: () => $pcFormField?.$pcForm?.states?.[$pcFormField?.name]?.value ?? [],
  set: (val) => {
    // 这里可直接调用 FormField 的内部方法或 emit 事件
    // @ts-ignore
    $pcFormField?.$pcForm?.setFieldValue?.($pcFormField?.name, val)
  },
})

// 监听内部状态变化，通知外部并同步到表单
watch(() => selectedTableData, (newVal) => {
  handleSelectionChange(newVal)
}, { deep: true })

// 选中行变化时同步到表单
function handleSelectionChange(val: BaseModel[]) {
  selectedRows.value = val
}
</script>

<template>
  <FlexRow align="start">
    <!-- left -->
    <DataTable
      :value="rawTableData"
      size="small"
      scrollable
      show-gridlines
      responsive-layout="scroll"
      class="flex-1"
    >
      <Column
        v-for="column in rawTableColumn"
        :key="column.dataIndex"
        :field="column.dataIndex as string"
        :header="column.title"
        :style="{ width: column.width ? `${column.width}px` : 'auto' }"
        :frozen="column.fixed === 'left'"
        :align-frozen="column.fixed === 'left' ? 'left' : 'right'"
      >
        <template #body="{ data }">
          <div v-if="column.dataIndex === 'action'">
            <Button
              :label="isItemSelected(data) ? '移除' : '选择'"
              :severity="isItemSelected(data) ? 'danger' : 'success'"
              size="small"
              @click="isItemSelected(data) ? removeItemBySource(data) : selectItem(data)"
            />
          </div>
          <div v-else-if="column.customRender && typeof column.customRender === 'function'">
            <component
              :is="(column.customRender as Function)({ record: data })"
            />
          </div>
          <div v-else>
            {{ data[column.dataIndex] }}
          </div>
        </template>
      </Column>

      <!-- 空数据状态 -->
      <template #empty>
        <div>
          {{ '暂无数据' }}
        </div>
      </template>
    </DataTable>

    <!-- right -->
    <DataTable
      :value="selectedTableData"
      size="small"
      scrollable
      :show-gridlines="true"
      responsive-layout="scroll"
      class="flex-1"
    >
      <Column
        v-for="column in selectedTableColumn"
        :key="column.dataIndex"
        :field="column.dataIndex as string"
        :header="column.title"
        :style="{ width: column.width ? `${column.width}px` : 'auto' }"
        :frozen="column.fixed === 'left'"
        :align-frozen="column.fixed === 'left' ? 'left' : 'right'"
      >
        <template #body="{ data }">
          <div v-if="column.dataIndex === 'action'">
            <Button
              label="移除"
              severity="danger"
              size="small"
              @click="removeItemByDestination(data)"
            />
          </div>
          <div v-else-if="column.customRender && typeof column.customRender === 'function'">
            <component
              :is="(column.customRender as Function)({ record: data })"
            />
          </div>
          <div v-else>
            {{ data[column.dataIndex] }}
          </div>
        </template>
      </Column>

      <!-- 空数据状态 -->
      <template #empty>
        <div>
          {{ '暂无数据' }}
        </div>
      </template>
    </DataTable>
  </FlexRow>
</template>
