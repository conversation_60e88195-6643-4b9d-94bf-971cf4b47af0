<script setup lang="ts">
import type { FormExpose } from '@/components/base/Form/type'
import type { TableColumn } from '@/service/base/interface/Table'
import { ref } from 'vue'

const formRef = ref<FormExpose<SupplierData>>()

// 定义供应商数据类型
interface SupplierData {
  id: number
  supplierCode: string
  supplierName: string
  supplierType: string
  contactPerson: string
  contactPhone: string
  contactEmail: string
  address: string
  creditLimit: number
  approvalStatus: string
  createTime: string
}

// 模拟供应商数据
const mockSuppliers: SupplierData[] = [
  {
    id: 1,
    supplierCode: 'SUP001',
    supplierName: '上海华通包装材料有限公司',
    supplierType: '包材供应商',
    contactPerson: '张经理',
    contactPhone: '021-12345678',
    contactEmail: '<EMAIL>',
    address: '上海市浦东新区张江高科技园区',
    creditLimit: 500000,
    approvalStatus: '已审批',
    createTime: '2024-01-15 10:30:00',
  },
  {
    id: 2,
    supplierCode: 'SUP002',
    supplierName: '广州优质纸业股份有限公司',
    supplierType: '原材料供应商',
    contactPerson: '李总监',
    contactPhone: '020-87654321',
    contactEmail: '<EMAIL>',
    address: '广州市天河区科技园区',
    creditLimit: 800000,
    approvalStatus: '已审批',
    createTime: '2024-01-20 14:20:00',
  },
  {
    id: 3,
    supplierCode: 'SUP003',
    supplierName: '深圳绿色环保包装有限公司',
    supplierType: '环保包材供应商',
    contactPerson: '王主管',
    contactPhone: '0755-98765432',
    contactEmail: '<EMAIL>',
    address: '深圳市南山区科技园',
    creditLimit: 300000,
    approvalStatus: '待审批',
    createTime: '2024-02-01 09:15:00',
  },
  {
    id: 4,
    supplierCode: 'SUP004',
    supplierName: '北京精工印刷设备有限公司',
    supplierType: '设备供应商',
    contactPerson: '赵工程师',
    contactPhone: '010-56789012',
    contactEmail: '<EMAIL>',
    address: '北京市海淀区中关村',
    creditLimit: 1200000,
    approvalStatus: '已审批',
    createTime: '2024-02-10 16:45:00',
  },
  {
    id: 5,
    supplierCode: 'SUP005',
    supplierName: '江苏强力胶粘剂科技有限公司',
    supplierType: '化工原料供应商',
    contactPerson: '陈博士',
    contactPhone: '025-34567890',
    contactEmail: '<EMAIL>',
    address: '江苏省南京市化工园区',
    creditLimit: 600000,
    approvalStatus: '已审批',
    createTime: '2024-02-15 11:30:00',
  },
  {
    id: 6,
    supplierCode: 'SUP006',
    supplierName: '浙江优选物流有限公司',
    supplierType: '物流服务商',
    contactPerson: '孙经理',
    contactPhone: '0571-23456789',
    contactEmail: '<EMAIL>',
    address: '浙江省杭州市萧山区',
    creditLimit: 200000,
    approvalStatus: '已审批',
    createTime: '2024-02-20 13:20:00',
  },
  {
    id: 7,
    supplierCode: 'SUP007',
    supplierName: '山东德州包装机械厂',
    supplierType: '机械设备供应商',
    contactPerson: '刘厂长',
    contactPhone: '0534-12345678',
    contactEmail: '<EMAIL>',
    address: '山东省德州市经济开发区',
    creditLimit: 900000,
    approvalStatus: '待审批',
    createTime: '2024-03-01 08:45:00',
  },
  {
    id: 8,
    supplierCode: 'SUP008',
    supplierName: '河南中原纸箱制造有限公司',
    supplierType: '包装制品供应商',
    contactPerson: '马总经理',
    contactPhone: '0371-87654321',
    contactEmail: '<EMAIL>',
    address: '河南省郑州市高新技术开发区',
    creditLimit: 450000,
    approvalStatus: '已审批',
    createTime: '2024-03-05 15:10:00',
  },
]

// 已选中的供应商（初始状态）
const selectedSuppliers = ref<SupplierData[]>([
  mockSuppliers[0], // 上海华通包装材料
  mockSuppliers[3], // 北京精工印刷设备
])

// 左侧表格列配置
const leftColumns: TableColumn<SupplierData>[] = [
  {
    title: '供应商编码',
    dataIndex: 'supplierCode',
    width: 120,
    fixed: 'left',
  },
  {
    title: '供应商名称',
    dataIndex: 'supplierName',
    width: 250,
    ellipsis: true,
  },
  {
    title: '供应商类型',
    dataIndex: 'supplierType',
    width: 150,
  },
  {
    title: '联系人',
    dataIndex: 'contactPerson',
    width: 100,
  },
  {
    title: '联系电话',
    dataIndex: 'contactPhone',
    width: 130,
  },
  {
    title: '联系邮箱',
    dataIndex: 'contactEmail',
    width: 200,
    ellipsis: true,
  },
  {
    title: '地址',
    dataIndex: 'address',
    width: 200,
    ellipsis: true,
  },
  {
    title: '审批状态',
    dataIndex: 'approvalStatus',
    width: 100,
    align: 'center',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 100,
  },
]

// 右侧表格列配置（简化版）
const rightColumns: TableColumn<SupplierData>[] = [
  {
    title: '供应商编码',
    dataIndex: 'supplierCode',
    width: 120,
    fixed: 'left',
  },
  {
    title: '供应商名称',
    dataIndex: 'supplierName',
    width: 250,
    ellipsis: true,
  },
  {
    title: '供应商类型',
    dataIndex: 'supplierType',
    width: 150,
  },
  {
    title: '联系人',
    dataIndex: 'contactPerson',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 100,
  },
]

// 统计信息
const statistics = computed(() => ({
  total: mockSuppliers.length,
  selected: selectedSuppliers.value.length,
  available: mockSuppliers.length - selectedSuppliers.value.length,
}))

// 全选
function selectAll() {
  selectedSuppliers.value = [...mockSuppliers]
}

// 清空
function clearAll() {
  selectedSuppliers.value = []
}

// 反选
function inverseSelection() {
  const unselectedSuppliers = mockSuppliers.filter(supplier =>
    !selectedSuppliers.value.some(selected => selected.id === supplier.id),
  )
  selectedSuppliers.value = [...unselectedSuppliers]
}

// 用于同步 FormField 值的引用
const formFieldValue = ref(JSON.stringify(selectedSuppliers.value))

// 监听 selectedSuppliers 变化，同步到 FormField
watch(selectedSuppliers, () => {
  formFieldValue.value = JSON.stringify(selectedSuppliers.value)
}, { deep: true })
</script>

<template>
  <div class="pick-table-demo">
    <div class="demo-header">
      <h2>穿梭表格组件示例</h2>
      <p>演示供应商选择场景，支持左右表格数据穿梭</p>
    </div>

    <!-- 统计信息 -->
    <div class="demo-statistics">
      <div class="stat-item">
        <span class="stat-label">总供应商数:</span>
        <span class="stat-value">{{ statistics.total }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">已选供应商:</span>
        <span class="stat-value">{{ statistics.selected }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">可选供应商:</span>
        <span class="stat-value">{{ statistics.available }}</span>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="demo-actions">
      <Button
        label="全选"
        severity="info"
        @click="selectAll"
      />
      <Button
        label="清空"
        severity="secondary"
        @click="clearAll"
      />
      <Button
        label="反选"
        severity="help"
        @click="inverseSelection"
      />
      <Button
        label="提交"
        severity="success"
        @click="async () => {
          const data = await formRef?.submit()
          console.log(data)
        }"
      />
    </div>

    <!-- 穿梭表格 -->
    <div class="demo-content">
      <Form ref="formRef">
        <FormItem name="wcnm" type="none">
          <PickTable
            :raw-table-data="mockSuppliers as unknown as Record<string, unknown>[]"
            :raw-table-column="leftColumns as unknown as TableColumn<Record<string, unknown>>[]"
            :selected-table-column="rightColumns as unknown as TableColumn<Record<string, unknown>>[]"
          />
        </FormItem>
      </Form>
    </div>

    <!-- 选中结果展示 -->
    <div class="demo-result">
      <h3>已选供应商汇总</h3>
      <div class="result-list">
        <div
          v-for="supplier in selectedSuppliers"
          :key="supplier.id"
          class="result-item"
        >
          <div class="supplier-info">
            <span class="supplier-name">{{ supplier.supplierName }}</span>
            <span class="supplier-code">{{ supplier.supplierCode }}</span>
            <span class="supplier-type">{{ supplier.supplierType }}</span>
          </div>
          <div class="supplier-credit">
            信用额度: ¥{{ supplier.creditLimit.toLocaleString() }}
          </div>
        </div>
        <div v-if="selectedSuppliers.length === 0" class="empty-result">
          暂未选择任何供应商
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.pick-table-demo {
  padding: 24px;
  max-width: 1600px;
  margin: 0 auto;
}

.demo-header {
  margin-bottom: 24px;
  text-align: center;
}

.demo-header h2 {
  margin: 0 0 8px 0;
  color: var(--text-color);
  font-size: 24px;
  font-weight: 600;
}

.demo-header p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 14px;
}

.demo-statistics {
  display: flex;
  gap: 32px;
  justify-content: center;
  margin-bottom: 24px;
  padding: 16px;
  background: var(--surface-50);
  border-radius: 8px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-color-secondary);
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: var(--primary-color);
}

.demo-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 24px;
}

.demo-content {
  margin-bottom: 24px;
  border: 1px solid var(--surface-border);
  border-radius: 8px;
  overflow: hidden;
}

.demo-result {
  background: var(--surface-50);
  border-radius: 8px;
  padding: 16px;
}

.demo-result h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: var(--text-color);
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: var(--surface-0);
  border-radius: 6px;
  border: 1px solid var(--surface-border);
}

.supplier-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.supplier-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
}

.supplier-code {
  font-size: 12px;
  color: var(--text-color-secondary);
}

.supplier-type {
  font-size: 12px;
  color: var(--primary-color);
  background: var(--primary-50);
  padding: 2px 6px;
  border-radius: 4px;
  align-self: flex-start;
}

.supplier-credit {
  font-size: 14px;
  font-weight: 600;
  color: var(--green-600);
}

.empty-result {
  text-align: center;
  padding: 40px;
  color: var(--text-color-secondary);
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pick-table-demo {
    padding: 16px;
  }

  .demo-statistics {
    flex-direction: column;
    gap: 16px;
  }

  .demo-actions {
    flex-wrap: wrap;
    justify-content: center;
  }

  .result-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
