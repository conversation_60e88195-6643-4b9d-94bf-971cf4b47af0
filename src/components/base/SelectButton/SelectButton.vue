<script setup lang="ts">
import type { SelectButtonProps } from './type'
import SelectButton from 'primevue/selectbutton'

const {
  options = [],
  multiple = false,
  disabled = false,
  size = 'middle',
  allowEmpty = true,
  dataKey,
  optionLabel,
  optionValue,
  optionDisabled,
} = defineProps<SelectButtonProps>()

const emits = defineEmits<{
  'update:modelValue': [value: number | number[] | string | string[] | undefined]
  'change': [value: number | number[] | string | string[] | undefined]
}>()

const modelValue = defineModel<number | number[] | string | string[] | undefined>()

// 将尺寸映射到 PrimeVue 的 size
const primeSize = computed(() => {
  switch (size) {
    case 'small':
      return 'small'
    case 'large':
      return 'large'
    case 'middle':
    default:
      return undefined
  }
})

function handleChange(value: number | number[] | string | string[] | undefined) {
  emits('update:modelValue', value)
  emits('change', value)
}
</script>

<template>
  <SelectButton
    v-model="modelValue"
    :options="options"
    :multiple="multiple"
    :disabled="disabled"
    :size="primeSize"
    :allow-empty="allowEmpty"
    :data-key="dataKey"
    :option-label="optionLabel"
    :option-value="optionValue"
    :option-disabled="optionDisabled"
    @update:model-value="handleChange"
  />
</template>
