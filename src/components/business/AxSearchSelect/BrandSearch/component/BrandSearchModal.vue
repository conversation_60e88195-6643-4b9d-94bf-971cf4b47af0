<script setup lang="ts">
import { rules } from '../config/rule'
import { BRAND_SEARCH_KEY, type BrandSearchService } from '../service/BrandSearchService'

// 获取服务实例
const brandService = inject<BrandSearchService>(BRAND_SEARCH_KEY)!
// 当前激活的标签页
const activeTab = ref('basic')
</script>

<template>
  <Dialog
    v-model:visible="brandService.formOpen" :header="brandService.formTitle"
  >
    <Form
      :ref="brandService.formRef" :form-data="brandService.formData" :rules="rules"
    >
      <!-- 标签页导航 -->
      <BaseTabs v-model:value="activeTab">
        <BaseTabList>
          <BaseTab value="basic">
            📋 基本信息
          </BaseTab>
          <BaseTab value="brand">
            🏷️ 品牌信息
          </BaseTab>
        </BaseTabList>

        <BaseTabPanels>
          <!-- 基本信息标签页 -->
          <BaseTabsPanel value="basic">
            <Grid :cols="2" :responsive="false">
              <GridCol>
                <FormItem label="品牌编码 (自动生成)" name="brandCode">
                  <InputText disabled />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="品牌名称" name="brandName">
                  <InputText placeholder="请输入品牌名称" />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="排序" name="sort">
                  <InputNumber placeholder="请输入排序值" :min="0" :max="9999" />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="状态" name="status">
                  <DictSelect key-code="brand_status" placeholder="请选择状态" />
                </FormItem>
              </GridCol>
            </Grid>
          </BaseTabsPanel>

          <!-- 品牌信息标签页 -->
          <BaseTabsPanel value="brand">
            <Grid :cols="2" :responsive="false">
              <GridCol>
                <FormItem label="品牌Logo" name="logoUrl">
                  <InputText placeholder="请输入Logo图片URL" allow-clear />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="官方网站" name="website">
                  <InputText placeholder="请输入官方网站地址" allow-clear />
                </FormItem>
              </GridCol>
              <GridCol :span="2">
                <FormItem label="品牌描述" name="description">
                  <CustomTextArea
                    placeholder="请输入品牌描述"
                    :rows="4"
                    :max-length="500"
                    show-count
                  />
                </FormItem>
              </GridCol>
            </Grid>
          </BaseTabsPanel>
        </BaseTabPanels>
      </BaseTabs>
    </Form>
    <template #footer>
      <Button label="取消" severity="danger" @click="brandService.closeForm()" />
      <Button label="保存" @click="brandService.submitFormAndRefresh" />
    </template>
  </Dialog>
</template>
