<script setup lang="ts">
import CustomerSearchModal from './component/CustomerSearchModal.vue'
import { CUSTOMER_SEARCH_KEY, CustomerSearchService } from './service/CustomerSearchService'

const { allowAdd = false, allowEdit = false, allowDelete = false } = defineProps<{
  allowAdd?: boolean
  allowEdit?: boolean
  allowDelete?: boolean
}>()

// 客户服务
const service = inject<CustomerSearchService>(CUSTOMER_SEARCH_KEY, new CustomerSearchService())!

provide(CUSTOMER_SEARCH_KEY, service)

// 初始化数据
onMounted(() => {
  service.queryList()
})
</script>

<template>
  <SearchSelect
    v-model="service.selectedKey"
    :options="service.listData || []"
    :option-label="service.labelKey"
    :option-value="service.valueKey"
    :placeholder="service.placeholder"
    :on-add="allowAdd ? () => service.openAddForm?.() : undefined"
    :on-edit="allowEdit ? (value: number) => service.openEditForm?.(value) : undefined"
    :on-delete="allowDelete ? (value: number) => service.deleteEntity?.(value) : undefined"
    v-bind="$attrs"
  />
  <CustomerSearchModal />
</template>
