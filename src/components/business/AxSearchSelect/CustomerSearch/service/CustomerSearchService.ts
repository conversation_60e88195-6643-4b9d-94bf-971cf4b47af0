import type { CustomerForm, CustomerPageParam, CustomerResult, CustomerQueryParam } from '@/api/business/customer/model/customer-ex'
import { customerApi } from '@/api/business/customer/customer-api'
import { ListCrudService } from '@/service/composite/ListCrudService'

export const CUSTOMER_SEARCH_KEY = Symbol('CUSTOMER_SEARCH_KEY')

/**
 * 客户服务
 * 提供客户相关的业务逻辑和数据管理
 */
export class CustomerSearchService extends ListCrudService<CustomerResult, CustomerForm, CustomerQueryParam> {
  constructor() {
    // 初始化服务
    super(
      '客户信息', // 业务名称
      {
        // 列表相关API
        queryList: customerApi.customerList,
        delete: customerApi.deleteCustomer,
        // 表单相关API
        getDetail: customerApi.customerDetail,
        add: customerApi.addCustomer,
        update: customerApi.updateCustomer,
      },
      {
        labelKey: 'customerName',
        valueKey: 'id',
      },
    )
  }

  // ---------------------------- 提供服务 ----------------------------
  provide() {
    provide(CUSTOMER_SEARCH_KEY, this)
  }
}