<script setup lang="ts">
import { rules } from '../config/rule'
import { CUSTOMER_SEARCH_KEY, type CustomerSearchService } from '../service/CustomerSearchService'

// 获取服务实例
const customerService = inject<CustomerSearchService>(CUSTOMER_SEARCH_KEY)!
// 当前激活的标签页
const activeTab = ref('basic')
</script>

<template>
  <Dialog
    v-model:visible="customerService.formOpen" :header="customerService.formTitle"
    width="1000px"
  >
    <Form
      :ref="customerService.formRef" :form-data="customerService.formData" :rules="rules"
    >
      <!-- 标签页导航 -->
      <BaseTabs v-model:value="activeTab">
        <BaseTabList>
          <BaseTab value="basic">
            📋 基本信息
          </BaseTab>
          <BaseTab value="contact">
            📞 联系信息
          </BaseTab>
          <BaseTab value="business">
            🏢 企业信息
          </BaseTab>
        </BaseTabList>

        <BaseTabPanels>
          <!-- 基本信息标签页 -->
          <BaseTabsPanel value="basic">
            <Grid :cols="2" :responsive="false">
              <GridCol>
                <FormItem label="客户编码 (自动生成)" name="customerCode">
                  <InputText disabled />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="客户名称" name="customerName">
                  <InputText placeholder="请输入客户名称" />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="客户类型" name="customerType">
                  <Select
                    placeholder="请选择客户类型"
                    :options="[
                      { label: '普通客户', value: 'REGULAR' },
                      { label: 'VIP客户', value: 'VIP' },
                      { label: '战略客户', value: 'STRATEGIC' },
                      { label: '经销商', value: 'DISTRIBUTOR' },
                      { label: '代理商', value: 'AGENT' },
                    ]"
                  />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="信用等级" name="creditLevel">
                  <Select
                    placeholder="请选择信用等级"
                    :options="[
                      { label: 'AAA级', value: 'AAA' },
                      { label: 'AA级', value: 'AA' },
                      { label: 'A级', value: 'A' },
                      { label: 'BBB级', value: 'BBB' },
                      { label: 'BB级', value: 'BB' },
                      { label: 'B级', value: 'B' },
                      { label: 'CCC级', value: 'CCC' },
                      { label: 'CC级', value: 'CC' },
                      { label: 'C级', value: 'C' },
                    ]"
                  />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="客户状态" name="status">
                  <Select
                    placeholder="请选择客户状态"
                    :options="[
                      { label: '活跃', value: 'ACTIVE' },
                      { label: '非活跃', value: 'INACTIVE' },
                      { label: '黑名单', value: 'BLACKLIST' },
                      { label: '潜在客户', value: 'POTENTIAL' },
                    ]"
                  />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="备注" name="remark">
                  <Textarea placeholder="请输入备注信息" rows="3" />
                </FormItem>
              </GridCol>
            </Grid>
          </BaseTabsPanel>

          <!-- 联系信息标签页 -->
          <BaseTabsPanel value="contact">
            <Grid :cols="2" :responsive="false">
              <GridCol>
                <FormItem label="联系人姓名" name="contactName">
                  <InputText placeholder="请输入联系人姓名" />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="联系职位" name="contactPosition">
                  <InputText placeholder="请输入联系人职位" />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="联系部门" name="contactDepartment">
                  <InputText placeholder="请输入联系人部门" />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="联系人电话" name="contactPhone">
                  <InputText placeholder="请输入联系人电话" />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="联系人手机" name="contactMobile">
                  <InputText placeholder="请输入联系人手机" />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="联系人邮箱" name="contactEmail">
                  <InputText placeholder="请输入联系人邮箱" />
                </FormItem>
              </GridCol>
            </Grid>
          </BaseTabsPanel>

          <!-- 企业信息标签页 -->
          <BaseTabsPanel value="business">
            <Grid :cols="2" :responsive="false">
              <GridCol>
                <FormItem label="营业执照号" name="businessLicense">
                  <InputText placeholder="请输入营业执照号" />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="税务登记号" name="taxId">
                  <InputText placeholder="请输入税务登记号" />
                </FormItem>
              </GridCol>
            </Grid>
          </BaseTabsPanel>
        </BaseTabPanels>
      </BaseTabs>
    </Form>

    <template #footer>
      <Button label="取消" severity="danger" @click="customerService.closeForm()" />
      <Button label="保存" @click="customerService.submitFormAndRefresh()" />
    </template>
  </Dialog>
</template>