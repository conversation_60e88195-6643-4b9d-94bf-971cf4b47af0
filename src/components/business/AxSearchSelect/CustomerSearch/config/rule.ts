import type { Rules } from '@/components/base/Form/type'

export const rules: Rules = {
  // 必填字段（basic标签页）- 只有必填字段才添加到rules中
  customerName: [
    { type: 'string', required: true, message: '请输入客户名称', trigger: 'blur' },
    { max: 100, message: '客户名称长度不能超过100个字符', trigger: 'blur' },
  ],
  customerType: [
    { type: 'string', required: true, message: '请选择客户类型', trigger: 'change' },
  ],
  status: [
    { type: 'string', required: true, message: '请选择客户状态', trigger: 'change' },
  ],
  contactName: [
    { type: 'string', required: true, message: '请输入联系人姓名', trigger: 'blur' },
    { max: 50, message: '联系人姓名长度不能超过50个字符', trigger: 'blur' },
  ],
  contactPhone: [
    { type: 'string', required: true, message: '请输入联系人电话', trigger: 'blur' },
    { max: 20, message: '联系人电话长度不能超过20个字符', trigger: 'blur' },
  ],
}

// 重要说明：
// 1. 🚨 选填字段不要放入rules中（会自动变成必填项）
// 2. 只有必填字段才添加到rules，并设置required: true
// 3. id、customerCode、createTime、updateTime等自动生成字段不需要验证规则
// 4. 每个字段都必须指定type属性，类型参考对应的Model定义