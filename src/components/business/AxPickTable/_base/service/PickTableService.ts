import type { TableColumn } from '@/service/base/interface/Table'
import type { PickTable, PickTableOptionsType } from './PickTable'

export const PICK_TABLE_KEY = Symbol('pickTableService')

export class PickTableService<S, G> implements PickTable<S, G> {
  private _sourceData = ref<S[]>([])

  private _selectedKeys = ref<number[]>([])

  private _selectedDataMap = ref<Map<S[keyof S] | G[keyof G], G>>(new Map())

  private _sourceColumns = ref<TableColumn[]>([])

  private _destinationColumns = ref<TableColumn[]>([])

  sourceRowKey: keyof S = 'id' as keyof S

  destinationRowKey: keyof G = 'id' as keyof G

  constructor(
    options: PickTableOptionsType<S, G>,
  ) {
    this._sourceData.value = options.sourceData
    this._sourceColumns.value = options.sourceColumns
    this._destinationColumns.value = options.destinationColumns || options.destinationColumnsFunc?.(this) || []
  }

  provide() {
    provide(PICK_TABLE_KEY, this)
  }

  // ==================== getter / setter ====================

  get sourceData(): S[] {
    return this._sourceData.value as S[]
  }

  get destinationKeys(): number[] {
    return this._selectedKeys.value
  }

  set destinationKeys(_value: number[]) {
    // if (!this.isItemSelected(this._selectedDataMap.value, value)) {
    //   this._selectedKeys.value.push(value)
    // }
  }

  get destinationDataMap(): Map<S[keyof S] | G[keyof G], G> {
    return this._selectedDataMap.value as Map<S[keyof S] | G[keyof G], G>
  }

  set destinationDataMap(value: Map<S[keyof S] | G[keyof G], G>) {
    this._selectedDataMap.value = value as Map<S[keyof S] | G[keyof G], G>
  }

  get destinationDataList(): G[] {
    return Array.from(toRaw(this._selectedDataMap.value).values()) as G[]
  }

  /**
   * 左边表格列配置 (只读)
   */
  get sourceColumns(): TableColumn[] {
    return this._sourceColumns.value
  }

  /**
   * 右边表格列配置 (只读)
   */
  get destinationColumns(): TableColumn[] {
    return this._destinationColumns.value
  }

  // ==================== 操作 ====================

  // 检查是否已选中
  isItemSelected(item: S): boolean {
    return this._selectedDataMap?.value?.has(item[this.sourceRowKey])
  }

  // 选中项目
  selectItem(item: S): void {
    // 选中前 钩子
    this.beforeSelectItem?.(this.destinationDataMap, item)
    // 如果已选中，直接返回
    if (this.isItemSelected(item))
      return

    const newMap = new Map([...this.destinationDataMap, [item[this.sourceRowKey], this.convertData(item)]])
    // 选中后 钩子
    this.afterSelectItem(newMap, item)
    this.destinationDataMap = newMap
  }

  // 移除项目
  removeItemBySource(item: S): void {
    // 移除前 钩子
    this.beforeRemoveItem(this.destinationDataMap, item)
    // 移除
    const newMap = new Map(this.destinationDataMap)
    newMap.delete(item[this.sourceRowKey])
    // 移除后 钩子
    this.afterRemoveItem(newMap, item)
    this.destinationDataMap = newMap
  }

  // 移除项目 (通过选中行)
  removeItemByDestination(item: G): void {
    // 移除
    const newMap = new Map(this.destinationDataMap)
    newMap.delete(item[this.destinationRowKey])
    this.destinationDataMap = newMap
  }

  // 清空选中
  clearSelected(): void {
    // 清空前 钩子
    this.beforeClearSelected(this.destinationDataMap)
    // 清空
    const newMap = new Map()
    // 清空后 钩子
    this.afterClearSelected(newMap)
    this.destinationDataMap = newMap
  }

  // ==================== 钩子 ====================
  /**
   * 选中项目前
   * @param selectedMap 选中数据
   * @param item 选中数据
   */
  protected beforeSelectItem(_selectedMap: Map<S[keyof S] | G[keyof G], G>, _item: S) { }

  /**
   * 选中项目后
   * @param selectedMap 选中数据
   * @param item 选中数据
   */
  protected afterSelectItem(_selectedMap: Map<S[keyof S] | G[keyof G], G>, _item: S) { }

  /**
   * 移除项目前
   * @param selectedMap 选中数据
   * @param item 选中数据
   */
  protected beforeRemoveItem(_selectedMap: Map<S[keyof S] | G[keyof G], G>, _item: S) { }

  /**
   * 移除项目后
   * @param selectedMap 选中数据
   * @param item 选中数据
   */
  protected afterRemoveItem(_selectedMap: Map<S[keyof S] | G[keyof G], G>, _item: S) { }

  /**
   * 清空选中前
   * @param selectedMap 选中数据
   */
  protected beforeClearSelected(_selectedMap: Map<S[keyof S] | G[keyof G], G>) { }

  /**
   * 清空选中后
   * @param selectedMap 选中数据
   */
  protected afterClearSelected(_selectedMap: Map<S[keyof S] | G[keyof G], G>) { }

  protected convertData(_data: S) {
    return _data as unknown as G
  }
}
