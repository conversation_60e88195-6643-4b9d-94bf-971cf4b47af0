import type { TableColumn } from '../../../../../service/base/interface/Table'

/**
 * 左边表格配置
 */
export interface SourceTableConfig<S> {
  /**
   * 表格行键
   */
  sourceRowKey: keyof S
  /**
   * 表格列配置
   */
  sourceColumns: TableColumn[]
  /**
   * 表格数据
   */
  sourceData: S[]
}

/**
 * 右边表格配置
 */
export interface DestinationTableConfig<S, G> {
  /**
   * 表格行键
   */
  destinationRowKey: keyof G
  /**
   * 选中行的key
   */
  destinationKeys: number[]
  /**
   * 选中行的数据
   */
  destinationDataMap: Map<S[keyof S] | G[keyof G], G>
  /**
   * 选中行的数据列表
   */
  destinationDataList: G[]
  /**
   * 表格列配置
   */
  destinationColumns: TableColumn[]
}

export interface PickTableAction<S, G> {
  /**
   * 检查是否已选中
   */
  isItemSelected: (item: S) => boolean
  /**
   * 选中项目
   */
  selectItem: (item: S) => void
  /**
   * 移除项目
   */
  removeItemBySource: (item: S) => void
  /**
   * 移除项目
   */
  removeItemByDestination: (item: G) => void
  /**
   * 清空选中
   */
  clearSelected: () => void
}
export interface PickTable<S, G> extends SourceTableConfig<S>, DestinationTableConfig<S, G>, PickTableAction<S, G> {}

export interface PickTableOptionsType<S, G> {
  sourceData: S[]
  sourceColumns: PickTable<S, G>['sourceColumns']
  destinationColumns?: PickTable<S, G>['destinationColumns']
  // @eslint-disable-next-line @typescript-eslint/no-explicit-any
  destinationColumnsFunc?: (service: any) => TableColumn[]
}
