import type { Sku<PERSON>ageParam, SkuResult } from '@/api/business/sku/model/sku-types'
import { skuApi } from '@/api/business/sku/sku-api'
import { TableService } from '@/service/base/TableService'
import { skuColumns } from '../config/columns'

export class SkuTableService extends TableService<SkuResult, SkuPageParam> {
  constructor() {
    super(
      'SKU表格',
      ref(skuColumns),
      {
        queryPage: skuApi.skuPage,
      },
    )
  }
}

export const skuTableService = new SkuTableService()
