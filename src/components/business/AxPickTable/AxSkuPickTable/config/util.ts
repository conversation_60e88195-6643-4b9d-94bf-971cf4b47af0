import type { SalesOrderItem } from '@/api/business/sales-order-item/model/sales-order-item-types'
import type { SkuResult } from '@/api/business/sku/model/sku-types'

export function createDefaultSalesOrderItem(): SalesOrderItem {
  return {
    lineNo: 0,
    productId: 0,
    productCode: '',
    productName: '',
    skuId: 0,
    skuCode: '',
    specSummary: '',
    unit: '',
    quantity: 0,
    unitPrice: 0,
    discountRate: 0,
    discountAmount: 0,
    lineAmount: 0,
    deliveryDate: '',
    deliveryQuantity: 0,
    remainingQuantity: 0,
    remark: '',
  }
}

export function skuResultToSalesOrderItem(skuResult: SkuResult): SalesOrderItem {
  return Object.assign(createDefaultSalesOrderItem(), {
    productName: skuResult.productName,
    skuId: skuResult.id,
    skuCode: skuResult.skuCode,
    specSummary: skuResult.specSummary,
    quantity: 1,
    unitPrice: skuResult.price || 0,
    discountRate: 1,
    discountAmount: 0,
    lineAmount: skuResult.price || 0,
    deliveryDate: '',
    deliveryQuantity: 0,
    remainingQuantity: 0,
    remark: '',
  })
}
