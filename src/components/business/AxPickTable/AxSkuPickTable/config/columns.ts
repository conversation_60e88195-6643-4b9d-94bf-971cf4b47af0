import type { SalesOrderItem } from '@/api/business/sales-order-item/model/sales-order-item-types'
import type { SkuResult } from '@/api/business/sku/model/sku-types'
import type { TableColumn } from '@/service/base/interface/Table'
import type { AxSkuPickTableService } from '../service/AxSkuPickTableService'
import CustomColorText from '@/components/base/CustomText/CustomColorText.vue'
import CustomText from '@/components/base/CustomText/CustomText.vue'
import ProductNameWithSpec from '@/components/business/ProductDisplay/ProductNameWithSpec.vue'
import QuantityInput from '@/components/business/TableInput/QuantityInput.vue'
import { h } from 'vue'

export const skuColumns = [
  {
    title: 'SKU编码',
    dataIndex: 'skuCode',
    key: 'skuCode',
    width: 100,
    fixed: 'left' as const,
  },
  {
    title: '商品名称',
    dataIndex: 'productName',
    key: 'productName',
    width: 180,
  },
  {
    title: '商品编码',
    dataIndex: 'productCode',
    key: 'productCode',
    width: 120,
  },
  {
    title: '规格摘要',
    dataIndex: 'specSummary',
    key: 'specSummary',
    width: 150,
    customRender: ({ record }: { record: SkuResult }) => {
      return h(CustomText, { value: record.specSummary })
    },
  },
  {
    title: '库存',
    dataIndex: 'stock',
    key: 'stock',
    width: 80,
    customRender: ({ record }: { record: SkuResult }) => {
      const stock = record.stock || 0
      const color = stock > 0 ? '#52c41a' : '#ff4d4f'
      return h(CustomColorText, { value: stock, color })
    },
  },
  {
    title: '重量(kg)',
    dataIndex: 'weight',
    key: 'weight',
    width: 100,
    customRender: ({ record }: { record: SkuResult }) => {
      const weightStr = record.weight ? `${record.weight}kg` : undefined
      return h(CustomText, { value: weightStr })
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 80,
    fixed: 'right' as const,
  },
]

export function createSelectedSkuColumns(service: AxSkuPickTableService): TableColumn<SalesOrderItem>[] {
  return [
    {
      title: '商品名称',
      dataIndex: 'productName',
      key: 'productName',
      width: 120,
      customRender: ({ record }: { record: SalesOrderItem }) => {
        return h(ProductNameWithSpec, { productName: record.productName, specSummary: record.specSummary })
      },
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      customRender: ({ record, index }: { record: SalesOrderItem, index?: number }) => {
        return h(QuantityInput, { value: record.quantity!, onChange: (value: number | null) => service.updateProductQuantity(index!, value || 1) })
      },
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 100,
      customRender: ({ record }: { record: SalesOrderItem }) => {
        return h(CustomText, { value: `¥${record.unitPrice!.toFixed(2)}` })
      },
    },
    {
      title: '小计',
      dataIndex: 'subtotal',
      key: 'subtotal',
      width: 100,
      customRender: ({ record }: { record: SalesOrderItem }) => {
        return h(CustomText, { value: `¥${record.lineAmount!.toFixed(2)}` })
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right' as const,
      width: 80,
    },
  ]
}
