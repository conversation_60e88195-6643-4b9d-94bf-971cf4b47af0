import type { <PERSON>mForm, BomPageParam, BomResult } from '@/api/business/bom/model/bom-ex'
import { bomApi } from '@/api/business/bom/bom-api'
import { TableCrudService as CrudService } from '@/service/composite/TableCrudService'
import { columns } from '../config/columns'

/**
 * BOM服务
 * 提供物料清单BOM相关的业务逻辑和数据管理
 */
export class BomService extends CrudService<BomResult, BomForm, BomPageParam> {
  constructor() {
    // 初始化服务
    super(
      '物料清单BOM', // 业务名称
      columns,
      {
        // 使用已有的API
        add: bomApi.addBom,
        queryPage: bomApi.bomPage,
        getDetail: bomApi.bomDetail,
        update: bomApi.updateBom,
        import: bomApi.importBom,
        export: bomApi.exportBom,
        delete: bomApi.deleteBom,
        batchDelete: bomApi.batchDeleteBom,
      }
    )
  }
}

// 单例模式
export const bomService = new BomService()
