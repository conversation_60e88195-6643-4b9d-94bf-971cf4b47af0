<script setup lang="ts">
import type { BomService } from '../service/BomService'
import { CRUD_KEY } from '@/service/composite/TableCrudService'

// 获取服务实例
const bomService = inject<BomService>(CRUD_KEY)!

// 当前激活的标签页
const activeTab = ref('basic')

// 详情页无需错误状态计算
</script>

<template>
  <Dialog 
    v-model:visible="bomService.detailOpen" 
    :header="bomService.detailTitle" 
    :show-footer="false" 
    width="1200px"
  >
    <!-- 详情分页容器 -->
    <BaseTabs v-model:value="activeTab">
      <!-- 标签页导航（无错误指示） -->
      <BaseTabList>
        <BaseTab value="basic">
          📋 基本信息
        </BaseTab>
        <BaseTab value="quantity">
          📊 数量信息
        </BaseTab>
        <BaseTab value="system">
          ⚙️ 系统信息
        </BaseTab>
      </BaseTabList>

      <!-- 标签页内容 -->
      <BaseTabPanels>
        <!-- 基本信息 -->
        <BaseTabsPanel value="basic">
          <Descriptions :column="2" :colon="false" bordered>
            <DescriptionsItem label="BOM ID">
              <Text :value="bomService.detailData.id?.toString()" />
            </DescriptionsItem>
            <DescriptionsItem label="成品SKU ID">
              <Text :value="bomService.detailData.parentSkuId?.toString()" />
            </DescriptionsItem>
            <DescriptionsItem label="组件SKU ID">
              <Text :value="bomService.detailData.childSkuId?.toString()" />
            </DescriptionsItem>
            <DescriptionsItem label="状态">
              <DictTag 
                :color="bomService.detailData.status === '1' ? 'success' : 'warning'"
                key-code="bom_status" 
                :value-code="bomService.detailData.status" 
              />
            </DescriptionsItem>
            <DescriptionsItem label="备注" :span="2">
              <Text :value="bomService.detailData.remark" />
            </DescriptionsItem>
          </Descriptions>
        </BaseTabsPanel>

        <!-- 数量信息 -->
        <BaseTabsPanel value="quantity">
          <Descriptions :column="2" :colon="false" bordered>
            <DescriptionsItem label="用量数量">
              <Text :value="bomService.detailData.quantity?.toString()" />
            </DescriptionsItem>
            <DescriptionsItem label="计量单位">
              <Text :value="bomService.detailData.unit" />
            </DescriptionsItem>
            <DescriptionsItem label="排序号">
              <Text :value="bomService.detailData.sortOrder?.toString()" />
            </DescriptionsItem>
          </Descriptions>
        </BaseTabsPanel>

        <!-- 系统信息 -->
        <BaseTabsPanel value="system">
          <Descriptions :column="2" :colon="false" bordered>
            <DescriptionsItem label="创建时间">
              <Text :value="formatDateTime(bomService.detailData.createTime)" />
            </DescriptionsItem>
            <DescriptionsItem label="更新时间">
              <Text :value="formatDateTime(bomService.detailData.updateTime)" />
            </DescriptionsItem>
            <DescriptionsItem label="创建用户ID">
              <Text :value="bomService.detailData.createUserId?.toString()" />
            </DescriptionsItem>
            <DescriptionsItem label="更新用户ID">
              <Text :value="bomService.detailData.updateUserId?.toString()" />
            </DescriptionsItem>
          </Descriptions>
        </BaseTabsPanel>
      </BaseTabPanels>
    </BaseTabs>
  </Dialog>
</template>
