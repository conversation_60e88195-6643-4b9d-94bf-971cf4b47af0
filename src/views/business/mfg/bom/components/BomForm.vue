<script setup lang="ts">
import type { BomService } from '../service/BomService'
import { CRUD_KEY } from '@/service/composite/TableCrudService'
// 表单验证规则
import { rules } from '../config/rule'

// 获取服务实例
const bomService = inject<BomService>(CRUD_KEY)!
// 当前激活的标签页
const activeTab = ref('basic')
</script>

<template>
  <Dialog
    v-model:visible="bomService.formOpen" 
    :header="bomService.formTitle"
  >
    <Form
      :ref="bomService.formRef" 
      :form-data="bomService.formData" 
      :rules="rules"
    >
      <!-- 标签页导航 -->
      <BaseTabs v-model:value="activeTab">
        <BaseTabList>
          <BaseTab value="basic">
            📋 基本信息
          </BaseTab>
          <BaseTab value="quantity">
            📊 数量信息
          </BaseTab>
          <BaseTab value="other">
            📝 其他信息
          </BaseTab>
        </BaseTabList>

        <BaseTabPanels>
          <!-- 基本信息标签页 -->
          <BaseTabsPanel value="basic">
            <Grid :cols="2" :responsive="false">
              <GridCol>
                <FormItem label="BOM ID (自动生成)" name="id">
                  <InputNumber disabled />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="成品SKU ID" name="parentSkuId">
                  <InputNumber placeholder="请输入成品SKU ID" allow-clear />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="组件SKU ID" name="childSkuId">
                  <InputNumber placeholder="请输入组件SKU ID" allow-clear />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="状态" name="status">
                  <DictSelect 
                    dict-code="bom_status" 
                    placeholder="请选择状态" 
                    allow-clear 
                  />
                </FormItem>
              </GridCol>
            </Grid>
          </BaseTabsPanel>

          <!-- 数量信息标签页 -->
          <BaseTabsPanel value="quantity">
            <Grid :cols="2" :responsive="false">
              <GridCol>
                <FormItem label="用量数量" name="quantity">
                  <InputNumber 
                    placeholder="请输入用量数量" 
                    allow-clear 
                    :min="0.01"
                    :precision="4"
                  />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="计量单位" name="unit">
                  <InputText placeholder="请输入计量单位" allow-clear />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="排序号" name="sortOrder">
                  <InputNumber 
                    placeholder="请输入排序号" 
                    allow-clear 
                    :min="0"
                    :precision="0"
                  />
                </FormItem>
              </GridCol>
            </Grid>
          </BaseTabsPanel>

          <!-- 其他信息标签页 -->
          <BaseTabsPanel value="other">
            <Grid :cols="1" :responsive="false">
              <GridCol>
                <FormItem label="备注" name="remark">
                  <InputTextarea 
                    placeholder="请输入备注" 
                    allow-clear 
                    :rows="4"
                    :maxlength="500"
                    show-count
                  />
                </FormItem>
              </GridCol>
            </Grid>
          </BaseTabsPanel>
        </BaseTabPanels>
      </BaseTabs>
    </Form>

    <template #footer>
      <Button label="取消" severity="danger" @click="bomService.closeForm()" />
      <Button label="保存" @click="bomService.submitFormAndRefresh()" />
    </template>
  </Dialog>
</template>
