import type { MaterialRequestItemResult } from '@/api/business/material-request-item/model/material-request-item-ex'
import type { DictColor } from '@/components/utils/Dict/type'
import type { TableColumn } from '@/service/base/interface/Table'
import { CustomText } from '@/components/base/CustomText'
import { DictTag } from '@/components/utils/Dict'
import { h } from 'vue'

// 获取状态对应的颜色
export function getStatusColor(status?: string): DictColor {
  switch (status) {
    case 'pending':
      return 'warning'
    case 'sufficient':
      return 'success'
    case 'insufficient':
      return 'error'
    default:
      return 'default'
  }
}

// 列定义
export const columns: TableColumn<MaterialRequestItemResult>[] = [
  {
    title: '物料确认ID',
    dataIndex: 'requestId',
    width: 120,
    sorter: true
  },
  {
    title: '物料SKU编码',
    dataIndex: 'materialSkuCode',
    width: 150,
    customRender: ({ record }: { record: MaterialRequestItemResult }) => {
      return h(CustomText, {
        value: record.materialSkuCode,
        copyable: true
      })
    }
  },
  {
    title: '物料SKU名称',
    dataIndex: 'materialSkuName',
    width: 200,
    ellipsis: true,
    customRender: ({ record }: { record: MaterialRequestItemResult }) => {
      return h(CustomText, {
        value: record.materialSkuName
      })
    }
  },
  {
    title: '需求数量',
    dataIndex: 'requiredQuantity',
    width: 120,
    align: 'right'
  },
  {
    title: '可用库存',
    dataIndex: 'availableQuantity',
    width: 120,
    align: 'right'
  },
  {
    title: '确认数量',
    dataIndex: 'confirmedQuantity',
    width: 120,
    align: 'right'
  },
  {
    title: '计量单位',
    dataIndex: 'unit',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 120,
    customRender: ({ record }: { record: MaterialRequestItemResult }) => {
      return h(DictTag, {
        color: getStatusColor(record.status),
        keyCode: 'material_item_status',
        valueCode: record.status
      })
    }
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
    ellipsis: true,
    customRender: ({ record }: { record: MaterialRequestItemResult }) => {
      return h(CustomText, {
        value: record.remark
      })
    }
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    sorter: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 200
  }
]