import type { Rules } from '@/components/base/Form/type'

export const rules: Rules = {
  // 必填字段（basic标签页）- 只有必填字段才添加到rules中
  requestId: [
    { type: 'number', required: true, message: '请选择物料确认单', trigger: 'change' }
  ],
  materialSkuId: [
    { type: 'number', required: true, message: '请选择物料SKU', trigger: 'change' }
  ],
  requiredQuantity: [
    { type: 'number', required: true, message: '请输入需求数量', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '需求数量必须大于0', trigger: 'blur' }
  ],
  status: [
    { type: 'string', required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 重要说明：
// 1. 🚨 选填字段不要放入rules中（会自动变成必填项）
// 2. 只有必填字段才添加到rules，并设置required: true
// 3. id、createTime、updateTime等自动生成字段不需要验证规则
// 4. 每个字段都必须指定type属性，类型参考对应的Model定义

