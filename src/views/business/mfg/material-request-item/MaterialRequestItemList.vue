<script setup lang="ts">
// 1. 导入区域
import MaterialRequestItemDetail from './components/MaterialRequestItemDetail.vue'
import MaterialRequestItemForm from './components/MaterialRequestItemForm.vue'
import { materialRequestItemService } from './service/MaterialRequestItemService'

// 2. 服务初始化
materialRequestItemService.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form :ref="materialRequestItemService.tableFormRef" v-privilege="'materialRequestItem:query'">
      <Grid>
        <GridCol>
          <FormItem label="物料确认ID" name="requestId">
            <InputNumber allow-clear placeholder="请输入物料确认ID" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="物料SKU编码" name="materialSkuCode">
            <InputText allow-clear placeholder="请输入物料SKU编码" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="物料SKU名称" name="materialSkuName">
            <InputText allow-clear placeholder="请输入物料SKU名称" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="状态" name="status">
            <DictSelect dict-code="material_item_status" allow-clear placeholder="请选择状态" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="创建时间" name="createTimeFrom">
            <DatePicker allow-clear placeholder="开始时间" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="至" name="createTimeTo">
            <DatePicker allow-clear placeholder="结束时间" />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons
          add-config="materialRequestItem:add"
          batch-delete-config="materialRequestItem:delete"
          import-config="materialRequestItem:import"
          export-config="materialRequestItem:export"
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton
          label="查询"
          icon-type="SearchOutlined"
          type="primary"
          @click="materialRequestItemService.onSearch"
        />
        <IconButton
          label="重置"
          icon-type="ReloadOutlined"
          @click="materialRequestItemService.resetQuery"
        />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable min-width="180rem">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'materialRequestItem:view'"
              label="查看"
              icon-type="EyeOutlined"
              @click="materialRequestItemService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'materialRequestItem:edit'"
              label="编辑"
              icon-type="EditOutlined"
              @click="materialRequestItemService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'materialRequestItem:delete'"
              label="删除"
              icon-type="DeleteOutlined"
              color="red"
              @click="materialRequestItemService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 弹窗组件 -->
  <MaterialRequestItemDetail />
  <MaterialRequestItemForm />
</template>