<script setup lang="ts">
import type { MaterialRequestItemService } from '../service/MaterialRequestItemService'
import { CRUD_KEY } from '@/service/composite/TableCrudService'

// 获取服务实例
const materialRequestItemService = inject<MaterialRequestItemService>(CRUD_KEY)!

// 当前激活的标签页
const activeTab = ref('basic')

// 详情页无需错误状态计算
</script>

<template>
  <Dialog
    v-model:visible="materialRequestItemService.detailOpen"
    :header="materialRequestItemService.detailTitle"
    :show-footer="false"
    width="1200px"
  >
    <!-- 详情分页容器 -->
    <BaseTabs v-model:value="activeTab">
      <!-- 标签页导航（无错误指示） -->
      <BaseTabList>
        <BaseTab value="basic">
          📋 基本信息
        </BaseTab>
        <BaseTab value="quantity">
          📊 数量信息
        </BaseTab>
        <BaseTab value="system">
          ⚙️ 系统信息
        </BaseTab>
      </BaseTabList>

      <!-- 标签页内容 -->
      <BaseTabPanels>
        <!-- 基本信息 -->
        <BaseTabsPanel value="basic">
          <Descriptions :column="2" :colon="false" bordered>
            <DescriptionsItem label="物料确认ID">
              <Text :value="materialRequestItemService.detailData.requestId" />
            </DescriptionsItem>
            <DescriptionsItem label="物料SKU ID">
              <Text :value="materialRequestItemService.detailData.materialSkuId" />
            </DescriptionsItem>
            <DescriptionsItem label="物料SKU编码">
              <Text :value="materialRequestItemService.detailData.materialSkuCode" />
            </DescriptionsItem>
            <DescriptionsItem label="物料SKU名称">
              <Text :value="materialRequestItemService.detailData.materialSkuName" />
            </DescriptionsItem>
            <DescriptionsItem label="计量单位">
              <Text :value="materialRequestItemService.detailData.unit" />
            </DescriptionsItem>
            <DescriptionsItem label="状态">
              <DictTag
                :key-code="'material_item_status'"
                :value-code="materialRequestItemService.detailData.status"
              />
            </DescriptionsItem>
          </Descriptions>
        </BaseTabsPanel>

        <!-- 数量信息 -->
        <BaseTabsPanel value="quantity">
          <Descriptions :column="2" :colon="false" bordered>
            <DescriptionsItem label="需求数量">
              <Text :value="materialRequestItemService.detailData.requiredQuantity" />
            </DescriptionsItem>
            <DescriptionsItem label="可用库存数量">
              <Text :value="materialRequestItemService.detailData.availableQuantity" />
            </DescriptionsItem>
            <DescriptionsItem label="确认数量">
              <Text :value="materialRequestItemService.detailData.confirmedQuantity" />
            </DescriptionsItem>
            <DescriptionsItem label="备注" :span="2">
              <Text :value="materialRequestItemService.detailData.remark" />
            </DescriptionsItem>
          </Descriptions>
        </BaseTabsPanel>

        <!-- 系统信息 -->
        <BaseTabsPanel value="system">
          <Descriptions :column="2" :colon="false" bordered>
            <DescriptionsItem label="ID">
              <Text :value="materialRequestItemService.detailData.id" />
            </DescriptionsItem>
            <DescriptionsItem label="创建时间">
              <Text :value="formatDateTime(materialRequestItemService.detailData.createTime)" />
            </DescriptionsItem>
            <DescriptionsItem label="更新时间">
              <Text :value="formatDateTime(materialRequestItemService.detailData.updateTime)" />
            </DescriptionsItem>
            <DescriptionsItem label="创建用户ID">
              <Text :value="materialRequestItemService.detailData.createUserId" />
            </DescriptionsItem>
            <DescriptionsItem label="更新用户ID">
              <Text :value="materialRequestItemService.detailData.updateUserId" />
            </DescriptionsItem>
          </Descriptions>
        </BaseTabsPanel>
      </BaseTabPanels>
    </BaseTabs>
  </Dialog>
</template>