<script setup lang="ts">
import type { MaterialRequestItemService } from '../service/MaterialRequestItemService'
import { CRUD_KEY } from '@/service/composite/TableCrudService'
// 表单验证规则
import { rules } from '../config/rule'

// 获取服务实例
const materialRequestItemService = inject<MaterialRequestItemService>(CRUD_KEY)!
// 当前激活的标签页
const activeTab = ref('basic')
</script>

<template>
  <Dialog
    v-model:visible="materialRequestItemService.formOpen"
    :header="materialRequestItemService.formTitle"
  >
    <Form
      :ref="materialRequestItemService.formRef"
      :form-data="materialRequestItemService.formData"
      :rules="rules"
    >
      <!-- 标签页导航 -->
      <BaseTabs v-model:value="activeTab">
        <BaseTabList>
          <BaseTab value="basic">
            📋 基本信息
          </BaseTab>
          <BaseTab value="quantity">
            📊 数量信息
          </BaseTab>
          <BaseTab value="other">
            📝 其他信息
          </BaseTab>
        </BaseTabList>

        <BaseTabPanels>
          <!-- 基本信息标签页 -->
          <BaseTabsPanel value="basic">
            <Grid :cols="2" :responsive="false">
              <GridCol>
                <FormItem label="物料确认ID" name="requestId">
                  <InputNumber placeholder="请输入物料确认ID" allow-clear />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="物料SKU ID" name="materialSkuId">
                  <InputNumber placeholder="请输入物料SKU ID" allow-clear />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="需求数量" name="requiredQuantity">
                  <InputNumber placeholder="请输入需求数量" allow-clear :min="0.01" />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="状态" name="status">
                  <DictSelect dict-code="material_item_status" placeholder="请选择状态" />
                </FormItem>
              </GridCol>
            </Grid>
          </BaseTabsPanel>

          <!-- 数量信息标签页 -->
          <BaseTabsPanel value="quantity">
            <Grid :cols="2" :responsive="false">
              <GridCol>
                <FormItem label="可用库存数量" name="availableQuantity">
                  <InputNumber placeholder="请输入可用库存数量" allow-clear :min="0" />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="确认数量" name="confirmedQuantity">
                  <InputNumber placeholder="请输入确认数量" allow-clear :min="0" />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="计量单位" name="unit">
                  <InputText placeholder="请输入计量单位" allow-clear />
                </FormItem>
              </GridCol>
            </Grid>
          </BaseTabsPanel>

          <!-- 其他信息标签页 -->
          <BaseTabsPanel value="other">
            <Grid :cols="2" :responsive="false">
              <GridCol>
                <FormItem label="物料SKU编码" name="materialSkuCode">
                  <InputText placeholder="请输入物料SKU编码" allow-clear />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="物料SKU名称" name="materialSkuName">
                  <InputText placeholder="请输入物料SKU名称" allow-clear />
                </FormItem>
              </GridCol>
              <GridCol :span="2">
                <FormItem label="备注" name="remark">
                  <Textarea placeholder="请输入备注" allow-clear :rows="3" />
                </FormItem>
              </GridCol>
            </Grid>
          </BaseTabsPanel>
        </BaseTabPanels>
      </BaseTabs>
    </Form>

    <template #footer>
      <Button label="取消" severity="danger" @click="materialRequestItemService.closeForm()" />
      <Button label="保存" @click="materialRequestItemService.submitFormAndRefresh()" />
    </template>
  </Dialog>
</template>