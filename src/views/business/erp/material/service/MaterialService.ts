import type { MaterialForm, MaterialPageParam, MaterialResult } from '@/api/business/material/model/material-ex'
import { materialApi } from '@/api/business/material/material-api'
import { TableCrudService as CrudService } from '@/service/composite/TableCrudService'
import { columns } from '../config/columns'

/**
 * 物料管理服务
 * 提供物料相关的业务逻辑和数据管理
 */
export class MaterialService extends CrudService<MaterialResult, MaterialForm, MaterialPageParam> {
  constructor() {
    // 初始化服务
    super(
      '物料', // 业务名称
      columns,
      {
        // 使用物料API
        add: materialApi.addMaterial,
        queryPage: materialApi.materialPage,
        getDetail: materialApi.materialDetail,
        update: materialApi.updateMaterial,
        import: materialApi.importMaterial,
        export: materialApi.exportMaterial,
        delete: materialApi.deleteMaterial,
        batchDelete: materialApi.batchDeleteMaterial,
      }
    )
  }
}

// 单例模式
export const materialService = new MaterialService()
