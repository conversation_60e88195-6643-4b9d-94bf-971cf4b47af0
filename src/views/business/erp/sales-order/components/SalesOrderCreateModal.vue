<script lang="ts" setup>
import { customerSearchServiceFormEx } from '../service/customerSearchFormServiceEx'
import { SALES_ORDER_CREATE_KEY, type SalesOrderCreateService } from '../service/salesOrderCreateService'
// 注入订单服务
const orderSkuService = inject<SalesOrderCreateService>(SALES_ORDER_CREATE_KEY)!

// 提供客户搜索服务
customerSearchServiceFormEx.provide()
</script>

<template>
  <Dialog
    v-model:visible="orderSkuService.createModalOpen"
    header="开单"
    class="w-[65%]"
  >
    <!-- 顶部客户选择区域 -->
    <Form :ref="orderSkuService.formRef">
      <Grid :cols="3" :responsive="false">
        <GridCol>
          <FormItem label="客户名称" name="customerId" class="w-[20vw]">
            <CustomerSearch />
          </FormItem>
        </GridCol>
        <GridCol :span="3">
          <FormItem label="商品名称" name="orderItemList" type="none">
            <AxSkuPickTable />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>

    <template #footer>
      <Button
        label="取消"
        severity="danger"
        @click="() => {
          orderSkuService.handleCloseModal()
          orderSkuService.formRef.value?.reset()
        }"
      />
      <Button
        label="提交订单"
        severity="success"
        @click="() => orderSkuService.submitFormAndRefresh()"
      />
    </template>
  </Dialog>
</template>
