import type { <PERSON>uForm, MenuQueryParam, MenuResult } from '@/api/system/menu/model/menu-ex'
import { menuApi } from '@/api/system/menu/menu-api'
import { TreeTableCrudService } from '@/service/composite/TreeTableCrudService'
import { columns } from '../config/column'
import { formDefault } from '../config/default'

/**
 * 菜单管理服务
 * 提供菜单相关的业务逻辑和数据管理
 */
export class MenuService extends TreeTableCrudService<MenuResult, MenuForm, MenuQueryParam> {
  constructor() {
    // 初始化服务
    super(
      '菜单', // 业务名称
      columns,
      {
        // 使用已有的API
        add: menuApi.addMenu,
        queryList: menuApi.queryMenuList,
        getDetail: menuApi.getMenuDetail,
        update: menuApi.updateMenu,
        delete: (id: number) => menuApi.batchDeleteMenu([id]),
        batchDelete: menuApi.batchDeleteMenu,
      },
      {
        rowKey: 'menuId',
      },
    )
  }

  protected getDefaultFormData(): MenuForm {
    return formDefault as unknown as MenuForm
  }

  protected beforeSubmit() {
    if (typeof this.formData.parentId === 'object') {
      if (Object.keys(this.formData.parentId).length > 0) {
        this.formData.parentId = Number(Object.keys(this.formData.parentId)[0])
      }
      else {
        this.formData.parentId = 0
      }
    }
    else {
      this.formData.parentId = 0
    }
  }

  protected afterOpenForm() {
    // 如果父级ID是数字，则转换为对象
    if (typeof this.formData.parentId === 'number') {
      if (this.formData.parentId !== 0) {
        // @ts-ignore
        this.formData.parentId = { [this.formData.parentId]: true }
      }
      else {
        this.formData.parentId = undefined
      }
    }
  }
}

// 单例模式
export const menuService = new MenuService()
