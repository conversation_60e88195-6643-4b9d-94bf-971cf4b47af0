<!--
 * 职务表单弹窗
-->
<script setup lang="ts">
import type { Position, PositionForm } from '@/api/system/position/position-api'
import { positionApi } from '@/api/system/position/position-api'
import { AxLoading } from '@/components/base/ax-loading'
import { sentry } from '@/lib/sentry'
import { useToast } from 'primevue/usetoast'

// 事件定义
const emit = defineEmits<{
  refresh: []
}>()

// 组件状态
const visible = ref(false)
const isEdit = ref(false)
const loading = ref(false)
const formRef = ref()
const toast = useToast()

// 表单数据
const formState = reactive<PositionForm>({
  positionId: undefined,
  positionName: '',
  level: '',
  sort: 0,
  remark: '',
})

// 表单默认值
const formDefault: PositionForm = {
  positionId: undefined,
  positionName: '',
  level: '',
  sort: 0,
  remark: '',
}

// 验证规则
const rules = {
  positionName: [{ required: true, message: '请输入职务名称' }],
  sort: [{ required: true, message: '请输入排序值' }],
}

// 显示新增弹窗
function showAddModal() {
  isEdit.value = false
  resetForm()
  visible.value = true
}

// 显示编辑弹窗
function showEditModal(record: Position) {
  isEdit.value = true
  resetForm()
  updateFormData(record)
  visible.value = true
}

// 更新表单数据
function updateFormData(data: Position) {
  Object.assign(formState, {
    positionId: data.positionId,
    positionName: data.positionName,
    level: data.level || '',
    sort: data.sort,
    remark: data.remark || '',
  })
}

// 重置表单
function resetForm() {
  Object.assign(formState, formDefault)
  formRef.value?.resetFields()
}

// 取消操作
function handleCancel() {
  visible.value = false
  resetForm()
}

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value.submit()
    await savePosition()
  }
  catch {
    toast.add({
      severity: 'error',
      summary: '表单验证失败',
      detail: '请检查表单数据',
      life: 3000,
    })
  }
}

// 保存职务
async function savePosition() {
  loading.value = true
  AxLoading.show()

  try {
    if (isEdit.value) {
      await positionApi.update(formState)
      toast.add({
        severity: 'success',
        summary: '编辑成功',
        life: 3000,
      })
    }
    else {
      await positionApi.add(formState)
      toast.add({
        severity: 'success',
        summary: '新增成功',
        life: 3000,
      })
    }

    emit('refresh')
    handleCancel()
  }
  catch (error) {
    sentry.captureError(error)
    toast.add({
      severity: 'error',
      summary: '操作失败',
      detail: '请重试或联系管理员',
      life: 3000,
    })
  }
  finally {
    loading.value = false
    AxLoading.hide()
  }
}

// 对外暴露方法
defineExpose({
  showAddModal,
  showEditModal,
})
</script>

<template>
  <Dialog
    v-model:visible="visible"
    :header="isEdit ? '编辑职务' : '新增职务'"
    modal
    :style="{ width: '500px' }"
    @hide="handleCancel"
  >
    <Form
      ref="formRef"
      :model="formState"
      :rules="rules"
      layout="vertical"
    >
      <FormItem label="职务名称" name="positionName">
        <InputText
          v-model.trim="formState.positionName"
          placeholder="请输入职务名称"
          class="w-full"
        />
      </FormItem>

      <FormItem label="职级" name="level">
        <InputText
          v-model.trim="formState.level"
          placeholder="请输入职级"
          class="w-full"
        />
      </FormItem>

      <FormItem label="排序" name="sort">
        <InputNumber
          v-model="formState.sort"
          placeholder="请输入排序值"
          :min="0"
          class="w-full"
        />
      </FormItem>

      <FormItem label="备注" name="remark">
        <CustomTextArea
          v-model="formState.remark"
          placeholder="请输入备注"
          :rows="3"
          class="w-full"
        />
      </FormItem>
    </Form>

    <template #footer>
      <div class="flex justify-end gap-2">
        <Button
          label="取消"
          severity="danger"
          @click="handleCancel"
        />
        <Button
          label="保存"
          :loading="loading"
          @click="handleSubmit"
        />
      </div>
    </template>
  </Dialog>
</template>
