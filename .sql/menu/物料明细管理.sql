-- 删除重复的物料明细管理菜单数据
DELETE FROM t_menu WHERE menu_name = '物料明细管理' AND deleted_flag = FALSE;
DELETE FROM t_menu WHERE api_perms LIKE 'materialRequestItem:%' AND deleted_flag = FALSE;

-- 物料明细管理菜单
-- 插入物料明细管理主菜单并获取ID
WITH material_request_item_menu AS (
    INSERT INTO t_menu (menu_name, menu_type, parent_id, path, component, perms_type, visible_flag, disabled_flag, deleted_flag, create_user_id)
    VALUES ('物料明细管理', 2, 0, '/material-request-item/list', '/business/mfg/material-request-item/MaterialRequestItemList.vue', 1, TRUE, FALSE, FALSE, 1)
    RETURNING menu_id
)
-- 批量插入物料明细管理权限菜单
INSERT INTO t_menu (menu_name, menu_type, parent_id, perms_type, api_perms, context_menu_id, visible_flag, disabled_flag, deleted_flag, create_user_id)
SELECT
    unnest(ARRAY['查询', '新增', '编辑', '删除', '导入', '导出']) as menu_name,
    3 as menu_type,
    material_request_item_menu.menu_id as parent_id,
    1 as perms_type,
    unnest(ARRAY['materialRequestItem:query', 'materialRequestItem:add', 'materialRequestItem:update', 'materialRequestItem:delete', 'materialRequestItem:import', 'materialRequestItem:export']) as api_perms,
    260 as context_menu_id,
    TRUE as visible_flag,
    FALSE as disabled_flag,
    FALSE as deleted_flag,
    1 as create_user_id
FROM material_request_item_menu;
